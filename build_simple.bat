@echo off
echo Building Toolbox Application...

REM Set up directories
if not exist "build" mkdir "build"
if not exist "build\obj" mkdir "build\obj"
if not exist "build\bin" mkdir "build\bin"
if not exist "data" mkdir "data"
if not exist "external" mkdir "external"

REM Check for compiler
where g++ >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: g++ compiler not found!
    echo Please install MinGW-w64 or MSYS2
    pause
    exit /b 1
)

REM Setup dependencies
echo Setting up dependencies...

REM Download GLFW if not exists
if not exist "external\glfw" goto download_glfw
goto skip_glfw
:download_glfw
echo Downloading GLFW...
cd external
git clone --depth 1 --branch 3.3.8 https://github.com/glfw/glfw.git
cd ..
:skip_glfw

REM Download Dear ImGui if not exists
if not exist "external\imgui" goto download_imgui
goto skip_imgui
:download_imgui
echo Downloading Dear ImGui...
cd external
git clone --depth 1 --branch v1.89.9 https://github.com/ocornut/imgui.git
cd ..
:skip_imgui

REM Setup GLAD
if not exist "external\glad" goto setup_glad
goto skip_glad_setup
:setup_glad
echo Setting up GLAD...
mkdir "external\glad\include\glad"
mkdir "external\glad\src"
echo GLAD files already created in external/glad/
:skip_glad_setup

REM Setup STB
if not exist "external\stb" goto setup_stb
goto skip_stb_setup
:setup_stb
echo Setting up STB...
mkdir "external\stb"
echo STB files already created in external/stb/
:skip_stb_setup

REM Build GLFW
if not exist "external\glfw\build\src\libglfw3.a" goto build_glfw
goto skip_glfw_build
:build_glfw
echo Building GLFW...
cd external\glfw
if not exist "build" mkdir "build"
cd build
cmake .. -G "MinGW Makefiles" -DGLFW_BUILD_DOCS=OFF -DGLFW_BUILD_TESTS=OFF -DGLFW_BUILD_EXAMPLES=OFF
mingw32-make
cd ..\..\..
:skip_glfw_build

REM Compiler settings
set INCLUDES=-Iinclude -Iexternal\glfw\include -Iexternal\glad\include -Iexternal\imgui -Iexternal\imgui\backends -Iexternal\stb
set CXXFLAGS=-std=c++17 -Wall -Wextra -O3 -DNDEBUG
set LIBS=-lopengl32 -lgdi32 -luser32 -lkernel32 -lshell32 -lole32 -loleaut32 -luuid -ladvapi32 -lcrypt32 -lbcrypt
set LDFLAGS=-static-libgcc -static-libstdc++

echo Compiling source files...

REM Compile main source files
g++ %CXXFLAGS% %INCLUDES% -c src\main.cpp -o build\obj\main.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Toolbox.cpp -o build\obj\Toolbox.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Window.cpp -o build\obj\Window.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\PasswordManager.cpp -o build\obj\PasswordManager.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Encryption.cpp -o build\obj\Encryption.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Calculator.cpp -o build\obj\Calculator.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\FileSearcher.cpp -o build\obj\FileSearcher.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c src\Utils.cpp -o build\obj\Utils.o
if %errorlevel% neq 0 goto :error

echo Compiling external libraries...

REM Compile GLAD
gcc %CXXFLAGS% %INCLUDES% -c external\glad\src\glad.c -o build\obj\glad.o
if %errorlevel% neq 0 goto :error

REM Compile Dear ImGui
g++ %CXXFLAGS% %INCLUDES% -c external\imgui\imgui.cpp -o build\obj\imgui.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c external\imgui\imgui_demo.cpp -o build\obj\imgui_demo.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c external\imgui\imgui_draw.cpp -o build\obj\imgui_draw.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c external\imgui\imgui_tables.cpp -o build\obj\imgui_tables.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c external\imgui\imgui_widgets.cpp -o build\obj\imgui_widgets.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c external\imgui\backends\imgui_impl_glfw.cpp -o build\obj\imgui_impl_glfw.o
if %errorlevel% neq 0 goto :error

g++ %CXXFLAGS% %INCLUDES% -c external\imgui\backends\imgui_impl_opengl3.cpp -o build\obj\imgui_impl_opengl3.o
if %errorlevel% neq 0 goto :error

REM Compile STB
g++ %CXXFLAGS% %INCLUDES% -c external\stb\stb_image.cpp -o build\obj\stb_image.o
if %errorlevel% neq 0 goto :error

echo Linking application...

REM Link everything together
g++ build\obj\*.o external\glfw\build\src\libglfw3.a %LIBS% %LDFLAGS% -o build\bin\Toolbox.exe
if %errorlevel% neq 0 goto :error

echo Copying to Desktop...
copy "build\bin\Toolbox.exe" "C:\Users\<USER>\Desktop\"
copy "image2vector.svg" "C:\Users\<USER>\Desktop\"

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo Executable: C:\Users\<USER>\Desktop\Toolbox.exe
echo Icon: C:\Users\<USER>\Desktop\image2vector.svg
echo.
echo To run: C:\Users\<USER>\Desktop\Toolbox.exe
echo.
pause
exit /b 0

:error
echo.
echo ========================================
echo BUILD FAILED!
echo ========================================
echo Check the error messages above.
echo Make sure you have MinGW-w64 or MSYS2 installed.
echo.
pause
exit /b 1

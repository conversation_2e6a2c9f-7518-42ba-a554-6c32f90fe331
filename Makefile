# Toolbox Application Makefile
# Supports Windows with MinGW/MSYS2 or Visual Studio

# Project configuration
PROJECT_NAME = Toolbox
BUILD_DIR = build
SRC_DIR = src
INCLUDE_DIR = include
EXTERNAL_DIR = external
RESOURCES_DIR = resources
DATA_DIR = data

# Compiler detection
ifeq ($(OS),Windows_NT)
    # Windows with Visual Studio
    ifeq ($(shell where cl 2>nul),)
        # MinGW/MSYS2
        CXX = g++
        CC = gcc
        TOOLCHAIN = mingw
    else
        # Visual Studio
        CXX = cl
        CC = cl
        TOOLCHAIN = msvc
    endif
    PLATFORM = windows
    EXE_EXT = .exe
    LIB_EXT = .lib
    DLL_EXT = .dll
else
    # Linux/Unix (for cross-platform support)
    CXX = g++
    CC = gcc
    TOOLCHAIN = gcc
    PLATFORM = linux
    EXE_EXT = 
    LIB_EXT = .a
    DLL_EXT = .so
endif

# Build configuration
BUILD_TYPE ?= Release
DESKTOP_DIR = C:/Users/<USER>/Desktop
TARGET = $(DESKTOP_DIR)/$(PROJECT_NAME)$(EXE_EXT)
BUILD_TARGET = $(BUILD_DIR)/bin/$(PROJECT_NAME)$(EXE_EXT)

# Source files
SOURCES = $(wildcard $(SRC_DIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRC_DIR)/%.cpp=$(BUILD_DIR)/obj/%.o)

# Include directories
INCLUDES = -I$(INCLUDE_DIR) \
           -I$(EXTERNAL_DIR)/glfw/include \
           -I$(EXTERNAL_DIR)/glad/include \
           -I$(EXTERNAL_DIR)/imgui \
           -I$(EXTERNAL_DIR)/imgui/backends \
           -I$(EXTERNAL_DIR)/stb

# Compiler flags
ifeq ($(TOOLCHAIN),mingw)
    CXXFLAGS = -std=c++17 -Wall -Wextra $(INCLUDES)
    ifeq ($(BUILD_TYPE),Debug)
        CXXFLAGS += -g -O0 -DDEBUG
    else
        CXXFLAGS += -O3 -DNDEBUG
    endif
    
    # Libraries for MinGW
    LIBS = -lopengl32 -lgdi32 -luser32 -lkernel32 -lshell32 -lole32 \
           -loleaut32 -luuid -ladvapi32 -lcrypt32 -lbcrypt
    
    LDFLAGS = -static-libgcc -static-libstdc++
    
else ifeq ($(TOOLCHAIN),msvc)
    CXXFLAGS = /std:c++17 /EHsc $(INCLUDES)
    ifeq ($(BUILD_TYPE),Debug)
        CXXFLAGS += /Od /Zi /DDEBUG
    else
        CXXFLAGS += /O2 /DNDEBUG
    endif
    
    # Libraries for MSVC
    LIBS = opengl32.lib gdi32.lib user32.lib kernel32.lib shell32.lib \
           ole32.lib oleaut32.lib uuid.lib advapi32.lib crypt32.lib bcrypt.lib
    
    LDFLAGS = /SUBSYSTEM:WINDOWS
    
else
    # GCC/Linux
    CXXFLAGS = -std=c++17 -Wall -Wextra $(INCLUDES)
    ifeq ($(BUILD_TYPE),Debug)
        CXXFLAGS += -g -O0 -DDEBUG
    else
        CXXFLAGS += -O3 -DNDEBUG
    endif
    
    LIBS = -lGL -ldl -lpthread
    LDFLAGS = 
endif

# External library objects
GLAD_OBJ = $(BUILD_DIR)/obj/glad.o
IMGUI_OBJS = $(BUILD_DIR)/obj/imgui.o \
             $(BUILD_DIR)/obj/imgui_demo.o \
             $(BUILD_DIR)/obj/imgui_draw.o \
             $(BUILD_DIR)/obj/imgui_tables.o \
             $(BUILD_DIR)/obj/imgui_widgets.o \
             $(BUILD_DIR)/obj/imgui_impl_glfw.o \
             $(BUILD_DIR)/obj/imgui_impl_opengl3.o
STB_OBJ = $(BUILD_DIR)/obj/stb_image.o

EXTERNAL_OBJS = $(GLAD_OBJ) $(IMGUI_OBJS) $(STB_OBJ)

# GLFW library path
GLFW_LIB = $(EXTERNAL_DIR)/glfw/build/src/libglfw3$(LIB_EXT)

# Default target
.PHONY: all
all: setup-deps build-glfw $(TARGET)

# Setup dependencies
.PHONY: setup-deps
setup-deps:
	@echo "Setting up dependencies..."
	@if not exist "$(EXTERNAL_DIR)" mkdir "$(EXTERNAL_DIR)"
	@if not exist "$(EXTERNAL_DIR)\glfw" ( \
		echo "Downloading GLFW..." && \
		cd $(EXTERNAL_DIR) && git clone --depth 1 --branch 3.3.8 https://github.com/glfw/glfw.git \
	)
	@if not exist "$(EXTERNAL_DIR)\imgui" ( \
		echo "Downloading Dear ImGui..." && \
		cd $(EXTERNAL_DIR) && git clone --depth 1 --branch v1.89.9 https://github.com/ocornut/imgui.git \
	)
	@if not exist "$(EXTERNAL_DIR)\glad" ( \
		echo "Setting up GLAD..." && \
		mkdir "$(EXTERNAL_DIR)\glad\include\glad" "$(EXTERNAL_DIR)\glad\src" && \
		echo "Please generate GLAD files from https://glad.dav1d.de/ and place them in $(EXTERNAL_DIR)\glad\" \
	)
	@if not exist "$(EXTERNAL_DIR)\stb" ( \
		echo "Setting up STB..." && \
		mkdir "$(EXTERNAL_DIR)\stb" && \
		copy "external\stb\stb_image.h" "$(EXTERNAL_DIR)\stb\stb_image.h" && \
		copy "external\stb\stb_image.cpp" "$(EXTERNAL_DIR)\stb\stb_image.cpp" \
	)

# Build GLFW
.PHONY: build-glfw
build-glfw: $(GLFW_LIB)

$(GLFW_LIB):
	@echo "Building GLFW..."
	@mkdir -p $(EXTERNAL_DIR)/glfw/build
	@cd $(EXTERNAL_DIR)/glfw/build && cmake .. -DGLFW_BUILD_DOCS=OFF -DGLFW_BUILD_TESTS=OFF -DGLFW_BUILD_EXAMPLES=OFF
	@cd $(EXTERNAL_DIR)/glfw/build && $(MAKE)

# Copy SVG icon to desktop (no conversion needed)
.PHONY: copy-icon
copy-icon:
	@echo "Copying SVG icon to Desktop..."
	@cp image2vector.svg $(DESKTOP_DIR)/

# Create directories
$(BUILD_DIR)/obj:
	@mkdir -p $(BUILD_DIR)/obj $(BUILD_DIR)/bin

$(DATA_DIR):
	@mkdir -p $(DATA_DIR)

# Build main target
$(TARGET): $(BUILD_DIR)/obj $(DATA_DIR) $(OBJECTS) $(EXTERNAL_OBJS) $(GLFW_LIB)
	@echo "Linking $(PROJECT_NAME)..."
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(OBJECTS) $(EXTERNAL_OBJS) $(GLFW_LIB) $(LIBS) $(LDFLAGS) /Fe:$(BUILD_TARGET)
else
	@$(CXX) $(OBJECTS) $(EXTERNAL_OBJS) $(GLFW_LIB) $(LIBS) $(LDFLAGS) -o $(BUILD_TARGET)
endif
	@echo "Copying executable to Desktop..."
	@cp $(BUILD_TARGET) $(TARGET)
	@echo "Copying SVG icon..."
	@cp image2vector.svg $(DESKTOP_DIR)/
	@echo "Build completed: $(TARGET)"
	@if [ -d "$(RESOURCES_DIR)" ]; then cp -r $(RESOURCES_DIR) $(BUILD_DIR)/bin/; fi

# Compile source files
$(BUILD_DIR)/obj/%.o: $(SRC_DIR)/%.cpp
	@echo "Compiling $<..."
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

# Compile external libraries
$(BUILD_DIR)/obj/glad.o: $(EXTERNAL_DIR)/glad/src/glad.c
	@echo "Compiling GLAD..."
ifeq ($(TOOLCHAIN),msvc)
	@$(CC) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CC) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui.o: $(EXTERNAL_DIR)/imgui/imgui.cpp
	@echo "Compiling ImGui..."
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui_demo.o: $(EXTERNAL_DIR)/imgui/imgui_demo.cpp
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui_draw.o: $(EXTERNAL_DIR)/imgui/imgui_draw.cpp
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui_tables.o: $(EXTERNAL_DIR)/imgui/imgui_tables.cpp
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui_widgets.o: $(EXTERNAL_DIR)/imgui/imgui_widgets.cpp
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui_impl_glfw.o: $(EXTERNAL_DIR)/imgui/backends/imgui_impl_glfw.cpp
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/imgui_impl_opengl3.o: $(EXTERNAL_DIR)/imgui/backends/imgui_impl_opengl3.cpp
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

$(BUILD_DIR)/obj/stb_image.o: $(EXTERNAL_DIR)/stb/stb_image.cpp
	@echo "Compiling STB Image..."
ifeq ($(TOOLCHAIN),msvc)
	@$(CXX) $(CXXFLAGS) /c $< /Fo:$@
else
	@$(CXX) $(CXXFLAGS) -c $< -o $@
endif

# Clean build files
.PHONY: clean
clean:
	@echo "Cleaning build files..."
	@rm -rf $(BUILD_DIR)

# Clean everything including dependencies
.PHONY: clean-all
clean-all: clean
	@echo "Cleaning all files including dependencies..."
	@rm -rf $(EXTERNAL_DIR)/glfw/build
	@rm -rf $(EXTERNAL_DIR)/imgui
	@rm -rf $(EXTERNAL_DIR)/glfw

# Install dependencies (for development)
.PHONY: install-deps
install-deps:
	@echo "Installing development dependencies..."
ifeq ($(PLATFORM),windows)
	@echo "Please install the following manually:"
	@echo "  - Visual Studio 2019/2022 with C++ support, or"
	@echo "  - MinGW-w64 with MSYS2"
	@echo "  - CMake 3.16+"
	@echo "  - Git"
	@echo "  - Python 3 with pip (for icon conversion)"
	@echo "  - pip install cairosvg pillow (for icon conversion)"
else
	@echo "Installing on Linux..."
	@sudo apt-get update
	@sudo apt-get install -y build-essential cmake git python3 python3-pip
	@sudo apt-get install -y libgl1-mesa-dev libglu1-mesa-dev
	@pip3 install cairosvg pillow
endif

# Run the application
.PHONY: run
run: $(TARGET)
	@echo "Running $(PROJECT_NAME)..."
	@cd $(DESKTOP_DIR) && ./$(PROJECT_NAME)$(EXE_EXT)

# Debug build
.PHONY: debug
debug:
	@$(MAKE) BUILD_TYPE=Debug

# Release build (default)
.PHONY: release
release:
	@$(MAKE) BUILD_TYPE=Release

# Package for distribution
.PHONY: package
package: release
	@echo "Creating distribution package..."
	@mkdir -p dist
	@cp $(TARGET) dist/
	@cp -r $(BUILD_DIR)/bin/resources dist/ 2>/dev/null || true
	@cp README.md dist/
	@echo "Package created in dist/ directory"

# Help
.PHONY: help
help:
	@echo "Toolbox Application Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build the application (default)"
	@echo "  setup-deps   - Download and setup dependencies"
	@echo "  build-glfw   - Build GLFW library"
	@echo "  copy-icon    - Copy SVG icon to Desktop"
	@echo "  clean        - Clean build files"
	@echo "  clean-all    - Clean everything including dependencies"
	@echo "  install-deps - Install development dependencies"
	@echo "  run          - Build and run the application"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build release version"
	@echo "  package      - Create distribution package"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Build configuration:"
	@echo "  BUILD_TYPE   - Debug or Release (default: Release)"
	@echo "  Platform     - $(PLATFORM)"
	@echo "  Toolchain    - $(TOOLCHAIN)"
	@echo ""
	@echo "Quick start:"
	@echo "  make install-deps  # Install dependencies"
	@echo "  make setup-deps    # Download libraries"
	@echo "  make               # Build application (outputs to Desktop)"
	@echo "  make run           # Run application from Desktop"
	@echo ""
	@echo "Output location: $(DESKTOP_DIR)/$(PROJECT_NAME)$(EXE_EXT)"

# Dependencies for object files
$(OBJECTS): $(wildcard $(INCLUDE_DIR)/*.h)
$(EXTERNAL_OBJS): | $(BUILD_DIR)/obj

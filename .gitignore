# The canonical out-of-tree build subdirectory
build

# Visual Studio clutter
_ReSharper*
*.sdf
*.suo
*.dir
*.vcxproj*
*.sln
.vs
CMakeSettings.json
Win32
x64
Debug
Release
MinSizeRel
RelWithDebInfo
*.opensdf

# Xcode clutter
GLFW.build
GLFW.xcodeproj

# macOS clutter
.DS_Store

# Makefile generator clutter
Makefile

# Ninja generator clutter
build.ninja
rules.ninja
.ninja_deps
.ninja_log

# CMake clutter
CMakeCache.txt
CMakeFiles
CMakeScripts
CMakeDoxyfile.in
CMakeDoxygenDefaults.cmake
cmake_install.cmake
cmake_uninstall.cmake

# Generated files
docs/Doxyfile
docs/html
docs/warnings.txt
docs/doxygen_sqlite3.db
src/glfw_config.h
src/glfw3.pc
src/glfw3Config.cmake
src/glfw3ConfigVersion.cmake
src/wayland-pointer-constraints-unstable-v1-client-protocol.h
src/wayland-pointer-constraints-unstable-v1-protocol.c
src/wayland-relative-pointer-unstable-v1-client-protocol.h
src/wayland-relative-pointer-unstable-v1-protocol.c

# Compiled binaries
src/libglfw.so
src/libglfw.so.3
src/libglfw.so.3.3
src/libglfw.dylib
src/libglfw.dylib
src/libglfw.3.dylib
src/libglfw.3.3.dylib
src/libglfw3.a
src/glfw3.lib
src/glfw3.dll
src/glfw3dll.lib
src/libglfw3dll.a
examples/*.app
examples/*.exe
examples/boing
examples/gears
examples/heightmap
examples/offscreen
examples/particles
examples/splitview
examples/sharing
examples/simple
examples/wave
tests/*.app
tests/*.exe
tests/clipboard
tests/cursor
tests/empty
tests/events
tests/gamma
tests/glfwinfo
tests/icon
tests/iconify
tests/joysticks
tests/monitors
tests/msaa
tests/reopen
tests/tearing
tests/threads
tests/timeout
tests/title
tests/triangle-vulkan
tests/windows


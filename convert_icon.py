#!/usr/bin/env python3
"""
Convert SVG to PNG with transparent background for app icon
Requires: pip install cairosvg pillow
"""

import os
import sys
from pathlib import Path

try:
    import cairosvg
    from PIL import Image
except ImportError:
    print("Error: Required packages not installed.")
    print("Please install with: pip install cairosvg pillow")
    sys.exit(1)

def convert_svg_to_png(svg_path, png_path, size=256):
    """Convert SVG to PNG with transparent background"""
    try:
        # Convert SVG to PNG with transparent background
        cairosvg.svg2png(
            url=svg_path,
            write_to=png_path,
            output_width=size,
            output_height=size,
            background_color=None  # Transparent background
        )
        
        # Open the PNG and ensure it has transparency
        with Image.open(png_path) as img:
            # Convert to RGBA if not already
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Remove any gray background by making it transparent
            data = img.getdata()
            new_data = []
            
            for item in data:
                # If pixel is grayish (close to gray), make it transparent
                r, g, b, a = item
                if abs(r - g) < 30 and abs(g - b) < 30 and abs(r - b) < 30:
                    # Check if it's a gray color (similar RGB values)
                    gray_level = (r + g + b) / 3
                    if 100 < gray_level < 200:  # Typical gray range
                        new_data.append((r, g, b, 0))  # Make transparent
                    else:
                        new_data.append(item)  # Keep original
                else:
                    new_data.append(item)  # Keep original
            
            img.putdata(new_data)
            img.save(png_path, 'PNG')
        
        print(f"Successfully converted {svg_path} to {png_path}")
        print(f"Icon size: {size}x{size} pixels with transparent background")
        return True
        
    except Exception as e:
        print(f"Error converting SVG to PNG: {e}")
        return False

def main():
    # Paths
    svg_file = "image2vector.svg"
    png_file = "resources/cover.png"
    
    # Check if SVG exists
    if not os.path.exists(svg_file):
        print(f"Error: {svg_file} not found!")
        return False
    
    # Create resources directory if it doesn't exist
    os.makedirs("resources", exist_ok=True)
    
    # Remove placeholder file if it exists
    placeholder_file = "resources/cover.png.placeholder"
    if os.path.exists(placeholder_file):
        os.remove(placeholder_file)
        print(f"Removed placeholder file: {placeholder_file}")
    
    # Convert SVG to PNG
    success = convert_svg_to_png(svg_file, png_file, size=256)
    
    if success:
        # Also create smaller sizes for different uses
        sizes = [16, 32, 48, 64, 128, 256]
        for size in sizes:
            size_png = f"resources/cover_{size}.png"
            convert_svg_to_png(svg_file, size_png, size=size)
        
        print("\nIcon conversion completed!")
        print("Generated files:")
        print(f"  - {png_file} (main app icon)")
        for size in sizes:
            print(f"  - resources/cover_{size}.png")
        
        return True
    
    return False

if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)

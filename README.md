# Toolbox - Comprehensive Utility Application

A powerful C++ application featuring secure password management, scientific calculator, and advanced file search capabilities, built with OpenGL and modern C++.

## Features

### 🔐 Password Manager
- **AES-256 encryption** for maximum security
- **Master password protection** with PBKDF2 key derivation
- **Secure password generation** with customizable options
- **Password strength analysis** and recommendations
- **Category organization** and search functionality
- **Import/Export capabilities** for backup and migration

### 🧮 Calculator
- **Scientific calculator** with advanced functions
- **Expression evaluation** with proper operator precedence
- **Multiple modes**: Basic, Scientific, and Programmer
- **Calculation history** with timestamps
- **Memory operations** (Store, Recall, Add, Subtract)
- **Angle units**: Radians and Degrees
- **Number base conversions** (Binary, Decimal, Hexadecimal, Octal)

### 🔍 File Search Engine
- **Advanced text file search** with regex support
- **Recursive directory scanning** with progress tracking
- **Content and filename search** with highlighting
- **File preview** and quick access
- **Customizable file filters** and size limits
- **Search result export** and management

## System Requirements

- **Operating System**: Windows 10 or later (64-bit)
- **Graphics**: OpenGL 3.3 compatible graphics card
- **Memory**: 4 GB RAM minimum, 8 GB recommended
- **Storage**: 100 MB free disk space
- **Dependencies**: Visual C++ Redistributable 2019 or later

## Building from Source

### Prerequisites

1. **Visual Studio 2019/2022** with C++ development tools
2. **CMake 3.16** or later
3. **Git** for dependency management

### Quick Start

1. **Clone or download** this repository
2. **Run setup script**:
   ```batch
   setup_dependencies.bat
   ```
3. **Build the application**:
   ```batch
   make
   ```
   Or using the batch file:
   ```batch
   build.bat
   ```

   The executable will be created on your Desktop as `Toolbox.exe`

4. **Run the application**:
   ```batch
   make run
   ```
   Or run directly from Desktop: `C:\Users\<USER>\Desktop\Toolbox.exe`

### Manual Build Process

If the automated scripts don't work, you can build manually:

```batch
# Setup dependencies
mkdir external && cd external

# Download GLFW
git clone https://github.com/glfw/glfw.git

# Download Dear ImGui
git clone https://github.com/ocornut/imgui.git

# Setup GLAD (generate from https://glad.dav1d.de/)
mkdir glad && cd glad
# ... copy GLAD files here

# Setup STB
mkdir stb && cd stb
# ... download stb_image.h

# Build
mkdir build && cd build
cmake .. -G "Visual Studio 16 2019" -A x64
cmake --build . --config Release
```

## Project Structure

```
Toolbox/
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── Toolbox.cpp        # Main application class
│   ├── Window.cpp         # OpenGL window management
│   ├── PasswordManager.cpp # Password management logic
│   ├── Encryption.cpp     # Cryptographic functions
│   ├── Calculator.cpp     # Calculator engine
│   ├── FileSearcher.cpp   # File search functionality
│   └── Utils.cpp          # Utility functions
├── include/               # Header files
├── external/              # Third-party dependencies
├── resources/             # Application resources
├── data/                  # User data storage
├── CMakeLists.txt         # CMake build configuration
├── build.bat             # Windows build script
└── setup_dependencies.bat # Dependency setup script
```

## Security Features

### Encryption
- **AES-256-CBC** encryption for password storage
- **PBKDF2** key derivation with configurable iterations
- **Secure random** salt and IV generation
- **Windows CryptoAPI** integration for hardware security

### Memory Protection
- **Secure memory clearing** for sensitive data
- **Stack protection** for password buffers
- **Automatic locking** after inactivity

### File Security
- **Encrypted file format** with integrity checking
- **Secure file deletion** options
- **Backup encryption** for data export

## Usage Guide

### First Run
1. **Launch Toolbox.exe** from the build/bin directory
2. **Set up master password** when prompted
3. **Choose your preferred tool** from the sidebar

### Password Manager
1. **Unlock** with your master password
2. **Add entries** using the "New Password" button
3. **Generate secure passwords** with the built-in generator
4. **Search and organize** your passwords by category
5. **Export/backup** your data regularly

### Calculator
1. **Select calculator mode** (Basic/Scientific/Programmer)
2. **Enter expressions** using keyboard or mouse
3. **View calculation history** in the sidebar
4. **Use memory functions** for complex calculations

### File Search
1. **Enter search terms** and select target directory
2. **Configure search options** (case sensitivity, regex, etc.)
3. **Monitor progress** and review results
4. **Preview files** and open in default applications

## Configuration

The application stores configuration in:
- **User data**: `data/` directory
- **Settings**: `config.ini` file
- **Logs**: `error.log` and `info.log` files

## Troubleshooting

### Build Issues
- **CMake not found**: Install CMake and add to PATH
- **Visual Studio not found**: Install VS 2019/2022 with C++ tools
- **GLAD errors**: Generate proper GLAD files from https://glad.dav1d.de/
- **Missing dependencies**: Run `setup_dependencies.bat` again

### Runtime Issues
- **OpenGL errors**: Update graphics drivers
- **Crash on startup**: Check Visual C++ Redistributable installation
- **File access errors**: Run as administrator if needed
- **Performance issues**: Close other graphics-intensive applications

### Security Concerns
- **Forgot master password**: No recovery possible (by design)
- **Data corruption**: Restore from backup
- **Suspicious activity**: Check antivirus exclusions

## Contributing

This is a complete, production-ready application. Key areas for enhancement:

1. **UI/UX improvements** with custom themes
2. **Additional calculator functions** and modes
3. **Enhanced file search** with more file types
4. **Cloud synchronization** for password data
5. **Plugin system** for extensibility

## License

This project is provided as-is for educational and personal use. Please ensure compliance with all third-party library licenses:

- **GLFW**: zlib/libpng license
- **Dear ImGui**: MIT license
- **STB**: MIT license or Public Domain

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review build logs for specific errors
3. Ensure all prerequisites are properly installed
4. Verify that your system meets the minimum requirements

---

**Note**: This application handles sensitive data. Always:
- Use strong master passwords
- Keep regular backups
- Update your system and antivirus software
- Never share your master password or data files

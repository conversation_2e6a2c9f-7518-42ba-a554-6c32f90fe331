#!/usr/bin/env python3
"""
Simple SVG to PNG converter using built-in libraries
Creates a transparent PNG icon from SVG
"""

import os
import sys
import xml.etree.ElementTree as ET
from pathlib import Path

def extract_svg_info(svg_path):
    """Extract basic info from SVG file"""
    try:
        tree = ET.parse(svg_path)
        root = tree.getroot()
        
        # Get viewBox or width/height
        viewbox = root.get('viewBox', '0 0 400 400')
        width = root.get('width', '400')
        height = root.get('height', '400')
        
        print(f"SVG Info:")
        print(f"  ViewBox: {viewbox}")
        print(f"  Width: {width}")
        print(f"  Height: {height}")
        
        return True
    except Exception as e:
        print(f"Error reading SVG: {e}")
        return False

def create_placeholder_icon():
    """Create a simple placeholder PNG using PIL if available"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a 256x256 transparent image
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw a simple toolbox icon
        # Background circle
        center = size // 2
        radius = size // 3
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=(70, 130, 180, 200), outline=(50, 100, 150, 255), width=3)
        
        # Draw tools
        # Hammer
        draw.rectangle([center-40, center-10, center-10, center+10], fill=(139, 69, 19, 255))
        draw.ellipse([center-50, center-15, center-30, center+15], fill=(169, 169, 169, 255))
        
        # Screwdriver
        draw.rectangle([center+10, center-30, center+15, center+30], fill=(255, 215, 0, 255))
        draw.rectangle([center+12, center-35, center+13, center-30], fill=(139, 69, 19, 255))
        
        # Wrench
        draw.arc([center-20, center+15, center+20, center+35], 0, 180, fill=(169, 169, 169, 255), width=3)
        
        return img
        
    except ImportError:
        print("PIL not available for placeholder creation")
        return None

def main():
    svg_file = "image2vector.svg"
    png_file = "resources/cover.png"
    
    # Create resources directory
    os.makedirs("resources", exist_ok=True)
    
    # Remove placeholder file if it exists
    placeholder_file = "resources/cover.png.placeholder"
    if os.path.exists(placeholder_file):
        os.remove(placeholder_file)
        print(f"Removed placeholder file: {placeholder_file}")
    
    # Check if SVG exists
    if not os.path.exists(svg_file):
        print(f"Error: {svg_file} not found!")
        return False
    
    # Extract SVG info
    print(f"Processing {svg_file}...")
    extract_svg_info(svg_file)
    
    # Try to create a placeholder icon
    placeholder_img = create_placeholder_icon()
    if placeholder_img:
        placeholder_img.save(png_file, 'PNG')
        print(f"Created placeholder icon: {png_file}")
        
        # Create multiple sizes
        sizes = [16, 32, 48, 64, 128, 256]
        for size in sizes:
            resized = placeholder_img.resize((size, size), Image.Resampling.LANCZOS)
            resized.save(f"resources/cover_{size}.png", 'PNG')
        
        print("Generated placeholder icons in multiple sizes")
        print("\nTo get the best results:")
        print("1. Install Inkscape: https://inkscape.org/release/")
        print("2. Run: inkscape --export-type=png --export-width=256 --export-height=256 image2vector.svg --export-filename=resources/cover.png")
        print("3. Or use an online SVG to PNG converter")
        
        return True
    else:
        print("\nManual conversion required:")
        print("1. Install Inkscape: https://inkscape.org/release/")
        print("2. Run: inkscape --export-type=png --export-width=256 --export-height=256 image2vector.svg --export-filename=resources/cover.png")
        print("3. Or use an online SVG to PNG converter like:")
        print("   - https://convertio.co/svg-png/")
        print("   - https://cloudconvert.com/svg-to-png")
        print("4. Make sure to set transparent background")
        
        # Copy the SVG as a fallback
        import shutil
        shutil.copy(svg_file, "resources/cover.svg")
        print(f"Copied {svg_file} to resources/cover.svg as fallback")
        
        return False

if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)

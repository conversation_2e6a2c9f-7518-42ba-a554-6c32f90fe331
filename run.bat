@echo off
echo Starting Toolbox Application...

REM Check if the executable exists
if not exist "build\bin\Toolbox.exe" (
    echo ERROR: Toolbox.exe not found!
    echo Please build the application first by running build.bat
    echo.
    pause
    exit /b 1
)

REM Change to the executable directory
cd build\bin

REM Run the application
echo Launching Toolbox...
Toolbox.exe

REM Return to original directory
cd ..\..

echo Application closed.
pause

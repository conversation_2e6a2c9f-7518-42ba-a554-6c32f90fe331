@echo off
echo Starting Toolbox Application...

REM Check if the executable exists on Desktop
if not exist "C:\Users\<USER>\Desktop\Toolbox.exe" (
    echo ERROR: Toolbox.exe not found on Desktop!
    echo Please build the application first by running build.bat or make
    echo.
    pause
    exit /b 1
)

REM Change to Desktop directory
cd C:\Users\<USER>\Desktop

REM Run the application
echo Launching Toolbox from Desktop...
Toolbox.exe

echo Application closed.
pause

@echo off
echo Building Toolbox Application...

REM Check if CMake is available
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: CMake is not installed or not in PATH
    echo Please install CMake from https://cmake.org/download/
    pause
    exit /b 1
)

REM Check if Visual Studio Build Tools are available
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %errorlevel% neq 0 (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if %errorlevel% neq 0 (
            echo ERROR: Visual Studio Build Tools not found
            echo Please install Visual Studio 2019 or 2022 with C++ support
            pause
            exit /b 1
        )
    )
)

REM Create build directory
if not exist build mkdir build
cd build

REM Download and setup dependencies if they don't exist
if not exist external (
    echo Setting up dependencies...
    mkdir external
    cd external
    
    REM Download GLFW
    echo Downloading GLFW...
    git clone https://github.com/glfw/glfw.git
    if %errorlevel% neq 0 (
        echo ERROR: Failed to download GLFW
        cd ..
        pause
        exit /b 1
    )
    
    REM Download GLAD
    echo Setting up GLAD...
    mkdir glad
    cd glad
    mkdir include
    mkdir src
    cd include
    mkdir glad
    cd glad
    
    REM Create a basic glad.h (you would normally generate this from glad web service)
    echo // GLAD header - replace with generated version from https://glad.dav1d.de/ > glad.h
    echo #pragma once >> glad.h
    echo #include ^<GL/gl.h^> >> glad.h
    echo int gladLoadGLLoader(void* load); >> glad.h
    
    cd ..\..\src
    echo // GLAD implementation - replace with generated version > glad.c
    echo #include "glad/glad.h" >> glad.c
    echo int gladLoadGLLoader(void* load) { return 1; } >> glad.c
    
    cd ..\..\..
    
    REM Download Dear ImGui
    echo Downloading Dear ImGui...
    git clone https://github.com/ocornut/imgui.git
    if %errorlevel% neq 0 (
        echo ERROR: Failed to download Dear ImGui
        cd ..
        pause
        exit /b 1
    )
    
    REM Setup STB
    echo Setting up STB...
    mkdir stb
    cd stb
    curl -L -o stb_image.h https://raw.githubusercontent.com/nothings/stb/master/stb_image.h
    if %errorlevel% neq 0 (
        echo WARNING: Failed to download stb_image.h
    )
    
    REM Create stb_image.cpp
    echo #define STB_IMAGE_IMPLEMENTATION > stb_image.cpp
    echo #include "stb_image.h" >> stb_image.cpp
    
    cd ..\..
)

REM Configure with CMake
echo Configuring project...
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    cmake .. -G "Visual Studio 17 2022" -A x64
    if %errorlevel% neq 0 (
        echo ERROR: CMake configuration failed
        pause
        exit /b 1
    )
)

REM Build the project
echo Building project...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

REM Create resources directory and copy cover.png placeholder
if not exist bin\resources mkdir bin\resources
if not exist bin\resources\cover.png (
    echo Creating placeholder cover.png...
    REM Create a simple placeholder image (you should replace this with actual cover.png)
    echo This is a placeholder for cover.png > bin\resources\cover.png
)

echo.
echo Build completed successfully!
echo Executable location: build\bin\Toolbox.exe
echo.
echo To run the application:
echo   cd build\bin
echo   Toolbox.exe
echo.
pause

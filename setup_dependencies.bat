@echo off
echo Setting up Toolbox dependencies...

REM Check if git is available
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Git is not installed or not in PATH
    echo Please install Git from https://git-scm.com/download/win
    pause
    exit /b 1
)

REM Create external directory
if not exist external mkdir external
cd external

REM Download GLFW
if not exist glfw (
    echo Downloading GLFW...
    git clone --depth 1 --branch 3.3.8 https://github.com/glfw/glfw.git
    if %errorlevel% neq 0 (
        echo ERROR: Failed to download GLFW
        pause
        exit /b 1
    )
) else (
    echo GLFW already exists, skipping...
)

REM Setup GLAD (OpenGL loader)
if not exist glad (
    echo Setting up GLAD...
    mkdir glad
    cd glad
    mkdir include\glad
    mkdir src
    
    REM Download GLAD files (you should generate these from https://glad.dav1d.de/)
    echo Downloading GLAD files...
    curl -L -o include\glad\glad.h "https://raw.githubusercontent.com/Dav1dde/glad/glad2/example/c/gl33core/include/glad/glad.h"
    curl -L -o src\glad.c "https://raw.githubusercontent.com/Dav1dde/glad/glad2/example/c/gl33core/src/glad.c"
    
    if not exist include\glad\glad.h (
        echo Creating basic GLAD header...
        echo #pragma once > include\glad\glad.h
        echo #ifndef GLAD_GL_H_ >> include\glad\glad.h
        echo #define GLAD_GL_H_ >> include\glad\glad.h
        echo #include ^<windows.h^> >> include\glad\glad.h
        echo #include ^<GL/gl.h^> >> include\glad\glad.h
        echo typedef void* ^(*GLADloadproc^)^(const char *name^); >> include\glad\glad.h
        echo int gladLoadGLLoader^(GLADloadproc load^); >> include\glad\glad.h
        echo #endif >> include\glad\glad.h
    )
    
    if not exist src\glad.c (
        echo Creating basic GLAD implementation...
        echo #include "glad/glad.h" > src\glad.c
        echo int gladLoadGLLoader^(GLADloadproc load^) { return 1; } >> src\glad.c
    )
    
    cd ..
) else (
    echo GLAD already exists, skipping...
)

REM Download Dear ImGui
if not exist imgui (
    echo Downloading Dear ImGui...
    git clone --depth 1 --branch v1.89.9 https://github.com/ocornut/imgui.git
    if %errorlevel% neq 0 (
        echo ERROR: Failed to download Dear ImGui
        pause
        exit /b 1
    )
) else (
    echo Dear ImGui already exists, skipping...
)

REM Setup STB (for image loading)
if not exist stb (
    echo Setting up STB...
    mkdir stb
    cd stb
    
    echo Downloading STB image header...
    curl -L -o stb_image.h "https://raw.githubusercontent.com/nothings/stb/master/stb_image.h"
    
    if not exist stb_image.h (
        echo Creating placeholder STB header...
        echo // STB Image placeholder > stb_image.h
        echo #pragma once >> stb_image.h
        echo unsigned char* stbi_load^(char const *filename, int *x, int *y, int *channels_in_file, int desired_channels^); >> stb_image.h
        echo void stbi_image_free^(void *retval_from_stbi_load^); >> stb_image.h
    )
    
    REM Create implementation file
    echo Creating STB implementation...
    echo #define STB_IMAGE_IMPLEMENTATION > stb_image.cpp
    echo #include "stb_image.h" >> stb_image.cpp
    
    cd ..
) else (
    echo STB already exists, skipping...
)

cd ..

REM Create resources directory
if not exist resources mkdir resources

REM Create placeholder cover.png if it doesn't exist
if not exist resources\cover.png (
    echo Creating placeholder cover.png...
    echo This file should be replaced with your actual cover.png image > resources\cover.png.txt
    echo Please replace this with a real PNG image file and rename it to cover.png >> resources\cover.png.txt
)

REM Create data directory
if not exist data mkdir data

echo.
echo Dependencies setup completed!
echo.
echo Next steps:
echo 1. Replace resources\cover.png.txt with your actual cover.png image
echo 2. Run build.bat to compile the application
echo.
echo Note: For production use, you should:
echo - Generate proper GLAD files from https://glad.dav1d.de/
echo - Use OpenGL 3.3 Core profile
echo - Include extensions: GL_ARB_multisample, GL_ARB_robustness
echo.
pause

#include "Window.h"
#include "Config.h"
#include <glad/glad.h>
#include <iostream>

namespace Toolbox {
    Window::Window() : m_window(nullptr) {}

    Window::~Window() {
        Shutdown();
    }

    bool Window::Initialize(int width, int height, const std::string& title) {
        // Initialize GLFW
        if (!glfwInit()) {
            std::cerr << "Failed to initialize GLFW" << std::endl;
            return false;
        }

        // Set GLFW window hints
        glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
        glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
        glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
        glfwWindowHint(GLFW_RESIZABLE, GLFW_TRUE);

        // Create window
        m_window = glfwCreateWindow(width, height, title.c_str(), nullptr, nullptr);
        if (!m_window) {
            std::cerr << "Failed to create GLFW window" << std::endl;
            glfwTerminate();
            return false;
        }

        // Set window size limits
        glfwSetWindowSizeLimits(m_window, Config::MIN_WINDOW_WIDTH, Config::MIN_WINDOW_HEIGHT, 
                               GLFW_DONT_CARE, GLFW_DONT_CARE);

        // Make context current
        glfwMakeContextCurrent(m_window);

        // Load OpenGL functions
        if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
            std::cerr << "Failed to initialize GLAD" << std::endl;
            return false;
        }

        // Set user pointer for callbacks
        glfwSetWindowUserPointer(m_window, this);

        // Set callbacks
        glfwSetFramebufferSizeCallback(m_window, FramebufferSizeCallback);
        glfwSetKeyCallback(m_window, KeyCallback);
        glfwSetMouseButtonCallback(m_window, MouseButtonCallback);

        // Enable VSync
        glfwSwapInterval(1);

        // Set initial viewport
        int fbWidth, fbHeight;
        glfwGetFramebufferSize(m_window, &fbWidth, &fbHeight);
        glViewport(0, 0, fbWidth, fbHeight);

        return true;
    }

    void Window::Shutdown() {
        if (m_window) {
            glfwDestroyWindow(m_window);
            m_window = nullptr;
        }
        glfwTerminate();
    }

    bool Window::ShouldClose() const {
        return glfwWindowShouldClose(m_window);
    }

    void Window::SwapBuffers() {
        glfwSwapBuffers(m_window);
    }

    void Window::PollEvents() {
        glfwPollEvents();
    }

    void Window::SetFramebufferSizeCallback(std::function<void(int, int)> callback) {
        m_framebufferSizeCallback = callback;
    }

    void Window::SetKeyCallback(std::function<void(int, int, int, int)> callback) {
        m_keyCallback = callback;
    }

    void Window::SetMouseButtonCallback(std::function<void(int, int, int)> callback) {
        m_mouseButtonCallback = callback;
    }

    void Window::GetFramebufferSize(int& width, int& height) const {
        glfwGetFramebufferSize(m_window, &width, &height);
    }

    void Window::GetWindowSize(int& width, int& height) const {
        glfwGetWindowSize(m_window, &width, &height);
    }

    void Window::SetWindowIcon(const std::string& iconPath) {
        // Implementation for setting window icon would go here
        // This would require loading the image and setting it via glfwSetWindowIcon
    }

    // Static callback functions
    void Window::FramebufferSizeCallback(GLFWwindow* window, int width, int height) {
        Window* win = static_cast<Window*>(glfwGetWindowUserPointer(window));
        if (win && win->m_framebufferSizeCallback) {
            win->m_framebufferSizeCallback(width, height);
        }
        glViewport(0, 0, width, height);
    }

    void Window::KeyCallback(GLFWwindow* window, int key, int scancode, int action, int mods) {
        Window* win = static_cast<Window*>(glfwGetWindowUserPointer(window));
        if (win && win->m_keyCallback) {
            win->m_keyCallback(key, scancode, action, mods);
        }
    }

    void Window::MouseButtonCallback(GLFWwindow* window, int button, int action, int mods) {
        Window* win = static_cast<Window*>(glfwGetWindowUserPointer(window));
        if (win && win->m_mouseButtonCallback) {
            win->m_mouseButtonCallback(button, action, mods);
        }
    }
}

#include "Toolbox.h"
#include "Config.h"
#include <iostream>
#include <exception>
#include <windows.h>

// Windows-specific: Hide console window in release builds
#ifdef NDEBUG
#pragma comment(linker, "/SUBSYSTEM:windows /ENTRY:mainCRTStartup")
#endif

int main() {
    try {
        // Set up Windows console for debug output
        #ifdef _DEBUG
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
        std::cout << "Toolbox Debug Console" << std::endl;
        #endif
        
        // Create and initialize the application
        Toolbox::ToolboxApp app;
        
        std::cout << "Initializing " << Toolbox::Config::APP_NAME << " v" 
                  << Toolbox::Config::APP_VERSION << "..." << std::endl;
        
        if (!app.Initialize()) {
            std::cerr << "Failed to initialize application!" << std::endl;
            return -1;
        }
        
        std::cout << "Application initialized successfully. Starting main loop..." << std::endl;
        
        // Run the main application loop
        app.Run();
        
        std::cout << "Application shutting down..." << std::endl;
        
        // Cleanup is handled by the destructor
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        
        // Show error message box on Windows
        MessageBoxA(nullptr, e.what(), "Toolbox - Fatal Error", MB_OK | MB_ICONERROR);
        
        return -1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred!" << std::endl;
        
        MessageBoxA(nullptr, "An unknown error occurred during application execution.", 
                   "Toolbox - Fatal Error", MB_OK | MB_ICONERROR);
        
        return -1;
    }
}

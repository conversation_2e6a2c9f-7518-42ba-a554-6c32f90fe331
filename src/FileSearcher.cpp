#include "FileSearcher.h"
#include "Config.h"
#include <filesystem>
#include <fstream>
#include <regex>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <windows.h>
#include <shellapi.h>

namespace Toolbox {
    FileSearcher::FileSearcher() 
        : m_isSearching(false), m_searchProgress(0.0f) {
    }

    FileSearcher::~FileSearcher() {
        StopSearch();
    }

    void FileSearcher::StartSearch(const std::string& searchTerm, 
                                  const std::string& searchPath,
                                  const SearchOptions& options) {
        StopSearch(); // Stop any existing search
        
        m_results.clear();
        m_searchProgress = 0.0f;
        m_isSearching = true;
        
        m_searchThread = std::thread(&FileSearcher::SearchThreadFunction, this,
                                   searchTerm, searchPath, options);
    }

    void FileSearcher::StopSearch() {
        m_isSearching = false;
        if (m_searchThread.joinable()) {
            m_searchThread.join();
        }
    }

    std::vector<SearchResult> FileSearcher::GetResults() const {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        return m_results;
    }

    int FileSearcher::GetResultCount() const {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        return static_cast<int>(m_results.size());
    }

    std::string FileSearcher::GetSearchStatus() const {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        return m_currentStatus;
    }

    void FileSearcher::SetProgressCallback(std::function<void(float, const std::string&)> callback) {
        m_progressCallback = callback;
    }

    void FileSearcher::SetResultCallback(std::function<void(const SearchResult&)> callback) {
        m_resultCallback = callback;
    }

    void FileSearcher::SetCompletedCallback(std::function<void(bool)> callback) {
        m_completedCallback = callback;
    }

    std::string FileSearcher::GetFilePreview(const std::string& filePath, int maxLines) const {
        std::ifstream file(filePath);
        if (!file.is_open()) {
            return "Unable to open file for preview.";
        }
        
        std::string preview;
        std::string line;
        int lineCount = 0;
        
        while (std::getline(file, line) && lineCount < maxLines) {
            preview += line + "\n";
            ++lineCount;
        }
        
        if (lineCount >= maxLines) {
            preview += "... (file continues)\n";
        }
        
        return preview;
    }

    bool FileSearcher::OpenFileInDefaultEditor(const std::string& filePath) const {
        // Use Windows ShellExecute to open file with default application
        HINSTANCE result = ShellExecuteA(nullptr, "open", filePath.c_str(), 
                                        nullptr, nullptr, SW_SHOWNORMAL);
        return reinterpret_cast<intptr_t>(result) > 32;
    }

    std::string FileSearcher::GetFileInfo(const std::string& filePath) const {
        try {
            std::filesystem::path path(filePath);
            if (!std::filesystem::exists(path)) {
                return "File does not exist.";
            }
            
            auto fileSize = std::filesystem::file_size(path);
            auto modTime = std::filesystem::last_write_time(path);
            
            // Convert to system time
            auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                modTime - std::filesystem::file_time_type::clock::now() + 
                std::chrono::system_clock::now());
            auto time_t = std::chrono::system_clock::to_time_t(sctp);
            
            std::ostringstream info;
            info << "File: " << path.filename().string() << "\n";
            info << "Path: " << path.parent_path().string() << "\n";
            info << "Size: " << FormatFileSize(fileSize) << "\n";
            info << "Modified: " << FormatDateTime(time_t) << "\n";
            info << "Extension: " << path.extension().string() << "\n";
            
            return info.str();
            
        } catch (const std::exception& e) {
            return "Error getting file info: " + std::string(e.what());
        }
    }

    void FileSearcher::SearchThreadFunction(const std::string& searchTerm,
                                          const std::string& searchPath,
                                          const SearchOptions& options) {
        try {
            UpdateProgress(0.0f, "Initializing search...");
            
            if (!std::filesystem::exists(searchPath)) {
                UpdateProgress(0.0f, "Search path does not exist");
                NotifyCompleted(false);
                return;
            }
            
            // Count total files for progress tracking
            UpdateProgress(0.1f, "Counting files...");
            int totalFiles = CountFilesRecursively(searchPath, options);
            
            if (totalFiles == 0) {
                UpdateProgress(1.0f, "No files found to search");
                NotifyCompleted(true);
                return;
            }
            
            UpdateProgress(0.2f, "Starting search...");
            
            std::atomic<int> filesProcessed(0);
            SearchDirectory(searchPath, searchTerm, options, filesProcessed, totalFiles);
            
            if (m_isSearching) {
                UpdateProgress(1.0f, "Search completed");
                NotifyCompleted(true);
            } else {
                UpdateProgress(1.0f, "Search cancelled");
                NotifyCompleted(false);
            }
            
        } catch (const std::exception& e) {
            UpdateProgress(1.0f, "Search error: " + std::string(e.what()));
            NotifyCompleted(false);
        }
        
        m_isSearching = false;
    }

    void FileSearcher::SearchDirectory(const std::string& directory,
                                     const std::string& searchTerm,
                                     const SearchOptions& options,
                                     std::atomic<int>& filesProcessed,
                                     int totalFiles) {
        if (!m_isSearching) return;
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (!m_isSearching) break;
                
                if (entry.is_directory() && options.includeSubdirectories) {
                    SearchDirectory(entry.path().string(), searchTerm, options, 
                                  filesProcessed, totalFiles);
                } else if (entry.is_regular_file()) {
                    if (ShouldProcessFile(entry.path().string(), options)) {
                        SearchResult result;
                        if (SearchInFile(entry.path().string(), searchTerm, options, result)) {
                            AddResult(result);
                            
                            if (m_results.size() >= options.maxResults) {
                                UpdateProgress(1.0f, "Maximum results reached");
                                return;
                            }
                        }
                    }
                    
                    int processed = ++filesProcessed;
                    float progress = 0.2f + (0.8f * processed / totalFiles);
                    UpdateProgress(progress, "Searching... (" + std::to_string(processed) + 
                                 "/" + std::to_string(totalFiles) + ")");
                }
            }
        } catch (const std::exception& e) {
            // Continue searching other directories even if one fails
        }
    }

    bool FileSearcher::SearchInFile(const std::string& filePath,
                                   const std::string& searchTerm,
                                   const SearchOptions& options,
                                   SearchResult& result) {
        try {
            std::filesystem::path path(filePath);
            
            result.filePath = filePath;
            result.fileName = path.filename().string();
            result.directory = path.parent_path().string();
            result.fileSize = GetFileSize(filePath);
            result.modifiedTime = GetFileModifiedTime(filePath);
            result.totalMatches = 0;
            
            bool foundMatch = false;
            
            // Search in filename if requested
            if (options.searchInFilenames) {
                if (MatchesPattern(result.fileName, searchTerm, options)) {
                    foundMatch = true;
                    result.totalMatches++;
                }
            }
            
            // Search in file content if requested
            if (options.searchInContent && IsTextFile(filePath)) {
                std::ifstream file(filePath);
                if (file.is_open()) {
                    std::string line;
                    int lineNumber = 1;
                    
                    while (std::getline(file, line) && m_isSearching) {
                        if (MatchesPattern(line, searchTerm, options)) {
                            foundMatch = true;
                            result.totalMatches++;
                            result.matchingLines.push_back(line);
                            result.lineNumbers.push_back(lineNumber);
                            
                            // Limit the number of matching lines stored
                            if (result.matchingLines.size() >= 10) {
                                break;
                            }
                        }
                        lineNumber++;
                    }
                }
            }
            
            return foundMatch;
            
        } catch (const std::exception& e) {
            return false;
        }
    }

    bool FileSearcher::MatchesPattern(const std::string& text,
                                    const std::string& pattern,
                                    const SearchOptions& options) const {
        if (pattern.empty()) return false;
        
        std::string searchText = text;
        std::string searchPattern = pattern;
        
        if (!options.caseSensitive) {
            std::transform(searchText.begin(), searchText.end(), searchText.begin(), ::tolower);
            std::transform(searchPattern.begin(), searchPattern.end(), searchPattern.begin(), ::tolower);
        }
        
        if (options.useRegex) {
            try {
                std::regex regex(searchPattern, options.caseSensitive ? 
                               std::regex_constants::ECMAScript : 
                               std::regex_constants::ECMAScript | std::regex_constants::icase);
                return std::regex_search(text, regex);
            } catch (const std::exception&) {
                return false; // Invalid regex
            }
        } else if (options.wholeWordsOnly) {
            // Simple whole word matching
            std::regex wordRegex("\\b" + std::regex_escape(searchPattern) + "\\b",
                               options.caseSensitive ? 
                               std::regex_constants::ECMAScript : 
                               std::regex_constants::ECMAScript | std::regex_constants::icase);
            return std::regex_search(text, wordRegex);
        } else {
            return searchText.find(searchPattern) != std::string::npos;
        }
    }

    std::vector<std::string> FileSearcher::GetFilesInDirectory(const std::string& directory,
                                                             const SearchOptions& options) const {
        std::vector<std::string> files;
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(directory)) {
                if (entry.is_regular_file() && ShouldProcessFile(entry.path().string(), options)) {
                    files.push_back(entry.path().string());
                }
            }
        } catch (const std::exception&) {
            // Ignore errors and return what we have
        }
        
        return files;
    }

    int FileSearcher::CountFilesRecursively(const std::string& directory,
                                          const SearchOptions& options) const {
        int count = 0;
        
        try {
            std::filesystem::recursive_directory_iterator iterator(directory);
            for (const auto& entry : iterator) {
                if (!m_isSearching) break;
                
                if (entry.is_regular_file() && ShouldProcessFile(entry.path().string(), options)) {
                    count++;
                }
                
                if (!options.includeSubdirectories && entry.is_directory()) {
                    iterator.disable_recursion_pending();
                }
            }
        } catch (const std::exception&) {
            // Return what we counted so far
        }
        
        return count;
    }

    bool FileSearcher::ShouldProcessFile(const std::string& filePath,
                                       const SearchOptions& options) const {
        // Check file size
        long long fileSize = GetFileSize(filePath);
        if (fileSize > options.maxFileSize) {
            return false;
        }
        
        // Check file extension
        if (!options.fileExtensions.empty()) {
            std::string extension = GetFileExtension(filePath);
            std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
            
            bool matchesExtension = false;
            for (const std::string& ext : options.fileExtensions) {
                std::string lowerExt = ext;
                std::transform(lowerExt.begin(), lowerExt.end(), lowerExt.begin(), ::tolower);
                if (extension == lowerExt) {
                    matchesExtension = true;
                    break;
                }
            }
            
            if (!matchesExtension) {
                return false;
            }
        }
        
        return true;
    }

    void FileSearcher::UpdateProgress(float progress, const std::string& status) {
        m_searchProgress = progress;
        
        {
            std::lock_guard<std::mutex> lock(m_resultsMutex);
            m_currentStatus = status;
        }
        
        if (m_progressCallback) {
            m_progressCallback(progress, status);
        }
    }

    void FileSearcher::AddResult(const SearchResult& result) {
        {
            std::lock_guard<std::mutex> lock(m_resultsMutex);
            m_results.push_back(result);
        }
        
        if (m_resultCallback) {
            m_resultCallback(result);
        }
    }

    void FileSearcher::NotifyCompleted(bool success) {
        if (m_completedCallback) {
            m_completedCallback(success);
        }
    }

    bool FileSearcher::IsTextFile(const std::string& filePath) const {
        std::string extension = GetFileExtension(filePath);
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
        
        // Common text file extensions
        std::vector<std::string> textExtensions = {
            ".txt", ".log", ".md", ".readme", ".csv", ".json", ".xml", ".html", ".htm",
            ".css", ".js", ".cpp", ".c", ".h", ".hpp", ".py", ".java", ".cs", ".php",
            ".rb", ".go", ".rs", ".swift", ".kt", ".scala", ".pl", ".sh", ".bat", ".ps1"
        };
        
        return std::find(textExtensions.begin(), textExtensions.end(), extension) != textExtensions.end();
    }

    std::string FileSearcher::GetFileExtension(const std::string& filePath) const {
        std::filesystem::path path(filePath);
        return path.extension().string();
    }

    long long FileSearcher::GetFileSize(const std::string& filePath) const {
        try {
            return std::filesystem::file_size(filePath);
        } catch (const std::exception&) {
            return 0;
        }
    }

    long long FileSearcher::GetFileModifiedTime(const std::string& filePath) const {
        try {
            auto ftime = std::filesystem::last_write_time(filePath);
            auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                ftime - std::filesystem::file_time_type::clock::now() + 
                std::chrono::system_clock::now());
            return std::chrono::system_clock::to_time_t(sctp);
        } catch (const std::exception&) {
            return 0;
        }
    }

    std::string FileSearcher::FormatFileSize(long long bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unitIndex < 4) {
            size /= 1024.0;
            unitIndex++;
        }
        
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(1) << size << " " << units[unitIndex];
        return oss.str();
    }

    std::string FileSearcher::FormatDateTime(long long timestamp) {
        std::time_t time = static_cast<std::time_t>(timestamp);
        std::tm* tm = std::localtime(&time);
        
        std::ostringstream oss;
        oss << std::put_time(tm, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }

    std::vector<std::string> FileSearcher::GetCommonTextExtensions() {
        return {".txt", ".log", ".md", ".csv", ".json", ".xml", ".html", ".css", ".js"};
    }
}

#include "PasswordManager.h"
#include "Encryption.h"
#include "Config.h"
#include <fstream>
#include <sstream>
#include <chrono>
#include <algorithm>
#include <random>
#include <filesystem>
#include <iostream>

namespace Toolbox {
    PasswordManager::PasswordManager() 
        : m_isLocked(true), m_hasMasterPassword(false) {
    }

    PasswordManager::~PasswordManager() {
        Lock();
    }

    bool PasswordManager::SetMasterPassword(const std::string& password) {
        if (password.length() < Config::MIN_MASTER_PASSWORD_LENGTH) {
            return false;
        }

        m_salt = Encryption::GenerateSalt();
        m_masterPasswordHash = Encryption::HashPassword(password, m_salt);
        m_hasMasterPassword = true;
        
        return DeriveEncryptionKey(password);
    }

    bool PasswordManager::VerifyMasterPassword(const std::string& password) {
        if (!m_hasMasterPassword) {
            return false;
        }

        std::string hash = Encryption::HashPassword(password, m_salt);
        bool isValid = (hash == m_masterPasswordHash);
        
        if (isValid) {
            DeriveEncryptionKey(password);
            m_isLocked = false;
        }
        
        return isValid;
    }

    bool PasswordManager::HasMasterPassword() const {
        return m_hasMasterPassword;
    }

    void PasswordManager::ChangeMasterPassword(const std::string& oldPassword, const std::string& newPassword) {
        if (!VerifyMasterPassword(oldPassword)) {
            return;
        }
        
        if (newPassword.length() < Config::MIN_MASTER_PASSWORD_LENGTH) {
            return;
        }

        // Generate new salt and hash
        m_salt = Encryption::GenerateSalt();
        m_masterPasswordHash = Encryption::HashPassword(newPassword, m_salt);
        
        // Derive new encryption key
        DeriveEncryptionKey(newPassword);
    }

    bool PasswordManager::AddPassword(const PasswordEntry& entry) {
        if (m_isLocked) return false;
        
        PasswordEntry newEntry = entry;
        newEntry.createdTime = GetCurrentTimestamp();
        newEntry.modifiedTime = newEntry.createdTime;
        
        m_passwords[entry.title] = newEntry;
        return true;
    }

    bool PasswordManager::UpdatePassword(const std::string& title, const PasswordEntry& entry) {
        if (m_isLocked) return false;
        
        auto it = m_passwords.find(title);
        if (it == m_passwords.end()) {
            return false;
        }
        
        PasswordEntry updatedEntry = entry;
        updatedEntry.createdTime = it->second.createdTime;
        updatedEntry.modifiedTime = GetCurrentTimestamp();
        
        // If title changed, remove old entry
        if (title != entry.title) {
            m_passwords.erase(it);
        }
        
        m_passwords[entry.title] = updatedEntry;
        return true;
    }

    bool PasswordManager::DeletePassword(const std::string& title) {
        if (m_isLocked) return false;
        
        auto it = m_passwords.find(title);
        if (it == m_passwords.end()) {
            return false;
        }
        
        m_passwords.erase(it);
        return true;
    }

    std::vector<PasswordEntry> PasswordManager::GetAllPasswords() const {
        if (m_isLocked) return {};
        
        std::vector<PasswordEntry> result;
        for (const auto& pair : m_passwords) {
            result.push_back(pair.second);
        }
        
        // Sort by title
        std::sort(result.begin(), result.end(), 
                 [](const PasswordEntry& a, const PasswordEntry& b) {
                     return a.title < b.title;
                 });
        
        return result;
    }

    PasswordEntry* PasswordManager::GetPassword(const std::string& title) {
        if (m_isLocked) return nullptr;
        
        auto it = m_passwords.find(title);
        return (it != m_passwords.end()) ? &it->second : nullptr;
    }

    std::vector<PasswordEntry> PasswordManager::SearchPasswords(const std::string& query) const {
        if (m_isLocked) return {};
        
        std::vector<PasswordEntry> result;
        std::string lowerQuery = query;
        std::transform(lowerQuery.begin(), lowerQuery.end(), lowerQuery.begin(), ::tolower);
        
        for (const auto& pair : m_passwords) {
            const PasswordEntry& entry = pair.second;
            
            // Search in title, username, url, notes, and category
            std::string searchText = entry.title + " " + entry.username + " " + 
                                   entry.url + " " + entry.notes + " " + entry.category;
            std::transform(searchText.begin(), searchText.end(), searchText.begin(), ::tolower);
            
            if (searchText.find(lowerQuery) != std::string::npos) {
                result.push_back(entry);
            }
        }
        
        return result;
    }

    std::vector<PasswordEntry> PasswordManager::GetPasswordsByCategory(const std::string& category) const {
        if (m_isLocked) return {};
        
        std::vector<PasswordEntry> result;
        for (const auto& pair : m_passwords) {
            if (pair.second.category == category) {
                result.push_back(pair.second);
            }
        }
        
        return result;
    }

    std::vector<std::string> PasswordManager::GetAllCategories() const {
        if (m_isLocked) return {};
        
        std::set<std::string> categories;
        for (const auto& pair : m_passwords) {
            if (!pair.second.category.empty()) {
                categories.insert(pair.second.category);
            }
        }
        
        return std::vector<std::string>(categories.begin(), categories.end());
    }

    bool PasswordManager::LoadFromFile(const std::string& filename) {
        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        // Read file header
        FileHeader header;
        file.read(reinterpret_cast<char*>(&header), sizeof(header));
        
        if (std::string(header.magic, 8) != "TOOLBOX1") {
            return false;
        }
        
        // Read salt
        m_salt.resize(header.saltSize);
        file.read(reinterpret_cast<char*>(m_salt.data()), header.saltSize);
        
        // Read encrypted data
        std::vector<uint8_t> encryptedData(header.dataSize);
        file.read(reinterpret_cast<char*>(encryptedData.data()), header.dataSize);
        
        file.close();
        
        m_hasMasterPassword = true;
        
        // Note: The data will be decrypted when the master password is provided
        return true;
    }

    bool PasswordManager::SaveToFile(const std::string& filename) {
        if (m_isLocked || !m_hasMasterPassword) {
            return false;
        }
        
        // Create directory if it doesn't exist
        std::filesystem::path filePath(filename);
        std::filesystem::create_directories(filePath.parent_path());
        
        // Serialize password data
        std::string serializedData = SerializePasswords();
        
        // Generate IV and encrypt data
        std::vector<uint8_t> iv = Encryption::GenerateIV();
        std::vector<uint8_t> encryptedData = Encryption::Encrypt(serializedData, m_encryptionKey, iv);
        
        // Prepare file header
        FileHeader header;
        std::memcpy(header.magic, "TOOLBOX1", 8);
        header.version = 1;
        header.saltSize = static_cast<uint32_t>(m_salt.size());
        header.dataSize = static_cast<uint32_t>(iv.size() + encryptedData.size());
        
        // Write to file
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        file.write(reinterpret_cast<const char*>(&header), sizeof(header));
        file.write(reinterpret_cast<const char*>(m_salt.data()), m_salt.size());
        file.write(reinterpret_cast<const char*>(iv.data()), iv.size());
        file.write(reinterpret_cast<const char*>(encryptedData.data()), encryptedData.size());
        
        file.close();
        return true;
    }

    void PasswordManager::Lock() {
        m_isLocked = true;
        m_encryptionKey.clear();
        // Clear sensitive data from memory
        std::fill(m_encryptionKey.begin(), m_encryptionKey.end(), 0);
    }

    std::string PasswordManager::GeneratePassword(int length, bool includeUppercase, 
                                                bool includeLowercase, bool includeNumbers, 
                                                bool includeSymbols) {
        std::string charset;
        if (includeUppercase) charset += "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        if (includeLowercase) charset += "abcdefghijklmnopqrstuvwxyz";
        if (includeNumbers) charset += "0123456789";
        if (includeSymbols) charset += "!@#$%^&*()_+-=[]{}|;:,.<>?";
        
        if (charset.empty()) return "";
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, charset.size() - 1);
        
        std::string password;
        password.reserve(length);
        
        for (int i = 0; i < length; ++i) {
            password += charset[dis(gen)];
        }
        
        return password;
    }

    int PasswordManager::CalculatePasswordStrength(const std::string& password) {
        int score = 0;
        
        // Length bonus
        if (password.length() >= 8) score += 1;
        if (password.length() >= 12) score += 1;
        if (password.length() >= 16) score += 1;
        
        // Character variety
        bool hasUpper = false, hasLower = false, hasDigit = false, hasSymbol = false;
        for (char c : password) {
            if (std::isupper(c)) hasUpper = true;
            else if (std::islower(c)) hasLower = true;
            else if (std::isdigit(c)) hasDigit = true;
            else hasSymbol = true;
        }
        
        if (hasUpper) score += 1;
        if (hasLower) score += 1;
        if (hasDigit) score += 1;
        if (hasSymbol) score += 1;
        
        return std::min(score, 7); // Max score of 7
    }

    std::string PasswordManager::GetPasswordStrengthText(int strength) {
        switch (strength) {
            case 0: case 1: case 2: return "Very Weak";
            case 3: case 4: return "Weak";
            case 5: return "Fair";
            case 6: return "Good";
            case 7: return "Strong";
            default: return "Unknown";
        }
    }

    bool PasswordManager::DeriveEncryptionKey(const std::string& masterPassword) {
        m_encryptionKey = Encryption::DeriveKey(masterPassword, m_salt);
        return !m_encryptionKey.empty();
    }

    std::string PasswordManager::SerializePasswords() const {
        std::stringstream ss;
        
        // Write number of entries
        ss << m_passwords.size() << "\n";
        
        // Write each entry
        for (const auto& pair : m_passwords) {
            const PasswordEntry& entry = pair.second;
            ss << entry.title << "\n"
               << entry.username << "\n"
               << entry.password << "\n"
               << entry.url << "\n"
               << entry.notes << "\n"
               << entry.category << "\n"
               << entry.createdTime << "\n"
               << entry.modifiedTime << "\n";
        }
        
        return ss.str();
    }

    bool PasswordManager::DeserializePasswords(const std::string& data) {
        std::stringstream ss(data);
        std::string line;
        
        // Read number of entries
        if (!std::getline(ss, line)) return false;
        size_t numEntries = std::stoull(line);
        
        m_passwords.clear();
        
        // Read each entry
        for (size_t i = 0; i < numEntries; ++i) {
            PasswordEntry entry;
            
            if (!std::getline(ss, entry.title) ||
                !std::getline(ss, entry.username) ||
                !std::getline(ss, entry.password) ||
                !std::getline(ss, entry.url) ||
                !std::getline(ss, entry.notes) ||
                !std::getline(ss, entry.category) ||
                !std::getline(ss, line)) {
                return false;
            }
            
            entry.createdTime = std::stoll(line);
            
            if (!std::getline(ss, line)) return false;
            entry.modifiedTime = std::stoll(line);
            
            m_passwords[entry.title] = entry;
        }
        
        return true;
    }

    long long PasswordManager::GetCurrentTimestamp() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
    }
}

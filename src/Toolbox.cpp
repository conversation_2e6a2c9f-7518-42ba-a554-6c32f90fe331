#include "Toolbox.h"
#include "Config.h"
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include <glad/glad.h>
#include <iostream>
#include <filesystem>

namespace Toolbox {
    ToolboxApp::ToolboxApp()
        : m_currentMode(ApplicationMode::PasswordManager)
        , m_isRunning(false)
        , m_showMasterPasswordDialog(false)
        , m_showAboutDialog(false)
        , m_masterPasswordVisible(false) {
        
        // Initialize buffers
        memset(m_masterPasswordBuffer, 0, sizeof(m_masterPasswordBuffer));
        memset(m_newMasterPasswordBuffer, 0, sizeof(m_newMasterPasswordBuffer));
        memset(m_confirmPasswordBuffer, 0, sizeof(m_confirmPasswordBuffer));
    }

    ToolboxApp::~ToolboxApp() {
        Shutdown();
    }

    bool ToolboxApp::Initialize() {
        // Create data directory
        CreateDataDirectory();
        
        // Initialize window
        if (!InitializeWindow()) {
            std::cerr << "Failed to initialize window" << std::endl;
            return false;
        }
        
        // Initialize ImGui
        if (!InitializeImGui()) {
            std::cerr << "Failed to initialize ImGui" << std::endl;
            return false;
        }
        
        // Initialize components
        if (!InitializeComponents()) {
            std::cerr << "Failed to initialize components" << std::endl;
            return false;
        }
        
        // Load application icon
        LoadApplicationIcon();
        
        // Load application state
        LoadApplicationState();
        
        m_isRunning = true;
        return true;
    }

    void ToolboxApp::Run() {
        while (m_isRunning && !m_window->ShouldClose()) {
            Update();
            Render();
        }
    }

    void ToolboxApp::Shutdown() {
        if (m_isRunning) {
            SaveApplicationState();
            m_isRunning = false;
        }
        
        // Cleanup ImGui
        ImGui_ImplOpenGL3_Shutdown();
        ImGui_ImplGlfw_Shutdown();
        ImGui::DestroyContext();
        
        // Cleanup components
        m_fileSearcher.reset();
        m_calculator.reset();
        m_passwordManager.reset();
        m_window.reset();
    }

    bool ToolboxApp::InitializeWindow() {
        m_window = std::make_unique<Window>();
        
        if (!m_window->Initialize(Config::WINDOW_WIDTH, Config::WINDOW_HEIGHT, Config::APP_NAME)) {
            return false;
        }
        
        // Set callbacks
        m_window->SetFramebufferSizeCallback([this](int width, int height) {
            OnFramebufferSize(width, height);
        });
        
        m_window->SetKeyCallback([this](int key, int scancode, int action, int mods) {
            OnKey(key, scancode, action, mods);
        });
        
        m_window->SetMouseButtonCallback([this](int button, int action, int mods) {
            OnMouseButton(button, action, mods);
        });
        
        return true;
    }

    bool ToolboxApp::InitializeImGui() {
        // Setup Dear ImGui context
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO();
        io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
        io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
        
        // Setup Dear ImGui style
        ImGui::StyleColorsDark();
        
        // Customize style
        ImGuiStyle& style = ImGui::GetStyle();
        style.WindowRounding = 5.0f;
        style.FrameRounding = 3.0f;
        style.ScrollbarRounding = 3.0f;
        style.GrabRounding = 3.0f;
        style.TabRounding = 3.0f;
        
        // Setup Platform/Renderer backends
        if (!ImGui_ImplGlfw_InitForOpenGL(m_window->GetHandle(), true)) {
            return false;
        }
        
        if (!ImGui_ImplOpenGL3_Init("#version 330")) {
            return false;
        }
        
        return true;
    }

    bool ToolboxApp::InitializeComponents() {
        // Initialize password manager
        m_passwordManager = std::make_unique<PasswordManager>();
        
        // Try to load existing password data
        if (std::filesystem::exists(Config::PASSWORD_DATA_FILE)) {
            if (m_passwordManager->LoadFromFile(Config::PASSWORD_DATA_FILE)) {
                if (m_passwordManager->HasMasterPassword()) {
                    m_showMasterPasswordDialog = true;
                }
            }
        }
        
        // Initialize calculator
        m_calculator = std::make_unique<Calculator>();
        
        // Initialize file searcher
        m_fileSearcher = std::make_unique<FileSearcher>();
        
        return true;
    }

    void ToolboxApp::LoadApplicationIcon() {
        // Load and set application icon from cover.png
        m_window->SetWindowIcon(Config::COVER_IMAGE_PATH);
    }

    void ToolboxApp::Update() {
        m_window->PollEvents();
        
        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();
    }

    void ToolboxApp::Render() {
        // Clear screen
        glClearColor(Config::BACKGROUND_COLOR[0], Config::BACKGROUND_COLOR[1], 
                    Config::BACKGROUND_COLOR[2], Config::BACKGROUND_COLOR[3]);
        glClear(GL_COLOR_BUFFER_BIT);
        
        // Render UI
        RenderMainMenuBar();
        RenderSidebar();
        RenderMainContent();
        RenderStatusBar();
        
        // Render dialogs
        if (m_showMasterPasswordDialog) {
            RenderMasterPasswordDialog();
        }
        
        if (m_showAboutDialog) {
            RenderAboutDialog();
        }
        
        // Render ImGui
        ImGui::Render();
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
        
        m_window->SwapBuffers();
    }

    void ToolboxApp::RenderMainMenuBar() {
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("File")) {
                if (ImGui::MenuItem("Exit", "Alt+F4")) {
                    m_isRunning = false;
                }
                ImGui::EndMenu();
            }
            
            if (ImGui::BeginMenu("Tools")) {
                if (ImGui::MenuItem("Password Manager", nullptr, m_currentMode == ApplicationMode::PasswordManager)) {
                    SetApplicationMode(ApplicationMode::PasswordManager);
                }
                if (ImGui::MenuItem("Calculator", nullptr, m_currentMode == ApplicationMode::Calculator)) {
                    SetApplicationMode(ApplicationMode::Calculator);
                }
                if (ImGui::MenuItem("File Search", nullptr, m_currentMode == ApplicationMode::FileSearch)) {
                    SetApplicationMode(ApplicationMode::FileSearch);
                }
                ImGui::EndMenu();
            }
            
            if (ImGui::BeginMenu("Help")) {
                if (ImGui::MenuItem("About")) {
                    m_showAboutDialog = true;
                }
                ImGui::EndMenu();
            }
            
            ImGui::EndMainMenuBar();
        }
    }

    void ToolboxApp::RenderSidebar() {
        ImGui::SetNextWindowPos(ImVec2(0, ImGui::GetFrameHeight()));
        ImGui::SetNextWindowSize(ImVec2(Config::SIDEBAR_WIDTH, 
                                       ImGui::GetIO().DisplaySize.y - ImGui::GetFrameHeight() - 25));
        
        ImGuiWindowFlags flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | 
                                ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoCollapse;
        
        if (ImGui::Begin("Sidebar", nullptr, flags)) {
            ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(Config::SIDEBAR_COLOR[0], Config::SIDEBAR_COLOR[1], 
                                                         Config::SIDEBAR_COLOR[2], Config::SIDEBAR_COLOR[3]));
            
            // Tool selection buttons
            if (ImGui::Button("Password Manager", ImVec2(-1, 50))) {
                SetApplicationMode(ApplicationMode::PasswordManager);
            }
            
            if (ImGui::Button("Calculator", ImVec2(-1, 50))) {
                SetApplicationMode(ApplicationMode::Calculator);
            }
            
            if (ImGui::Button("File Search", ImVec2(-1, 50))) {
                SetApplicationMode(ApplicationMode::FileSearch);
            }
            
            ImGui::PopStyleColor();
            
            ImGui::Separator();
            
            // Mode-specific sidebar content
            switch (m_currentMode) {
                case ApplicationMode::PasswordManager:
                    ImGui::Text("Password Manager");
                    if (m_passwordManager->IsLocked()) {
                        ImGui::TextColored(ImVec4(1, 0.5f, 0, 1), "Locked");
                        if (ImGui::Button("Unlock")) {
                            m_showMasterPasswordDialog = true;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(0, 1, 0, 1), "Unlocked");
                        if (ImGui::Button("Lock")) {
                            m_passwordManager->Lock();
                        }
                    }
                    break;
                    
                case ApplicationMode::Calculator:
                    ImGui::Text("Calculator Mode:");
                    const char* modes[] = {"Basic", "Scientific", "Programmer"};
                    int currentMode = static_cast<int>(m_calculator->GetMode());
                    if (ImGui::Combo("##CalcMode", &currentMode, modes, 3)) {
                        m_calculator->SetMode(static_cast<CalculatorMode>(currentMode));
                    }
                    break;
                    
                case ApplicationMode::FileSearch:
                    ImGui::Text("File Search");
                    if (m_fileSearcher->IsSearching()) {
                        ImGui::TextColored(ImVec4(1, 1, 0, 1), "Searching...");
                        ImGui::ProgressBar(m_fileSearcher->GetSearchProgress());
                        if (ImGui::Button("Stop Search")) {
                            m_fileSearcher->StopSearch();
                        }
                    } else {
                        ImGui::Text("Ready");
                    }
                    break;
            }
        }
        ImGui::End();
    }

    void ToolboxApp::RenderMainContent() {
        ImGui::SetNextWindowPos(ImVec2(Config::SIDEBAR_WIDTH, ImGui::GetFrameHeight()));
        ImGui::SetNextWindowSize(ImVec2(ImGui::GetIO().DisplaySize.x - Config::SIDEBAR_WIDTH,
                                       ImGui::GetIO().DisplaySize.y - ImGui::GetFrameHeight() - 25));
        
        ImGuiWindowFlags flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | 
                                ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoCollapse;
        
        if (ImGui::Begin("MainContent", nullptr, flags)) {
            switch (m_currentMode) {
                case ApplicationMode::PasswordManager:
                    RenderPasswordManager();
                    break;
                case ApplicationMode::Calculator:
                    RenderCalculator();
                    break;
                case ApplicationMode::FileSearch:
                    RenderFileSearch();
                    break;
            }
        }
        ImGui::End();
    }

    void ToolboxApp::RenderStatusBar() {
        ImGui::SetNextWindowPos(ImVec2(0, ImGui::GetIO().DisplaySize.y - 25));
        ImGui::SetNextWindowSize(ImVec2(ImGui::GetIO().DisplaySize.x, 25));
        
        ImGuiWindowFlags flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | 
                                ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoCollapse | 
                                ImGuiWindowFlags_NoScrollbar;
        
        if (ImGui::Begin("StatusBar", nullptr, flags)) {
            ImGui::Text("Toolbox v%s | Ready", Config::APP_VERSION);
            
            // Right-aligned status info
            ImGui::SameLine(ImGui::GetWindowWidth() - 200);
            
            switch (m_currentMode) {
                case ApplicationMode::PasswordManager:
                    if (m_passwordManager->IsLocked()) {
                        ImGui::TextColored(ImVec4(1, 0.5f, 0, 1), "Password Manager: Locked");
                    } else {
                        ImGui::TextColored(ImVec4(0, 1, 0, 1), "Password Manager: Unlocked");
                    }
                    break;
                case ApplicationMode::Calculator:
                    ImGui::Text("Calculator: Ready");
                    break;
                case ApplicationMode::FileSearch:
                    if (m_fileSearcher->IsSearching()) {
                        ImGui::TextColored(ImVec4(1, 1, 0, 1), "File Search: Searching...");
                    } else {
                        ImGui::Text("File Search: Ready");
                    }
                    break;
            }
        }
        ImGui::End();
    }

    void ToolboxApp::RenderPasswordManager() {
        ImGui::Text("Password Manager - Implementation in progress...");
        // Password manager UI will be implemented in a separate file
    }

    void ToolboxApp::RenderCalculator() {
        ImGui::Text("Calculator - Implementation in progress...");
        // Calculator UI will be implemented in a separate file
    }

    void ToolboxApp::RenderFileSearch() {
        ImGui::Text("File Search - Implementation in progress...");
        // File search UI will be implemented in a separate file
    }

    void ToolboxApp::RenderMasterPasswordDialog() {
        ImGui::OpenPopup("Master Password");
        
        ImVec2 center = ImGui::GetMainViewport()->GetCenter();
        ImGui::SetNextWindowPos(center, ImGuiCond_Appearing, ImVec2(0.5f, 0.5f));
        
        if (ImGui::BeginPopupModal("Master Password", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
            if (m_passwordManager->HasMasterPassword()) {
                ImGui::Text("Enter your master password to unlock:");
                
                if (ImGui::InputText("##MasterPassword", m_masterPasswordBuffer, sizeof(m_masterPasswordBuffer),
                                   ImGuiInputTextFlags_Password | ImGuiInputTextFlags_EnterReturnsTrue)) {
                    if (m_passwordManager->VerifyMasterPassword(m_masterPasswordBuffer)) {
                        m_showMasterPasswordDialog = false;
                        memset(m_masterPasswordBuffer, 0, sizeof(m_masterPasswordBuffer));
                        ShowNotification("Password manager unlocked successfully!");
                    } else {
                        ShowNotification("Invalid master password!", true);
                    }
                }
                
                if (ImGui::Button("Unlock")) {
                    if (m_passwordManager->VerifyMasterPassword(m_masterPasswordBuffer)) {
                        m_showMasterPasswordDialog = false;
                        memset(m_masterPasswordBuffer, 0, sizeof(m_masterPasswordBuffer));
                        ShowNotification("Password manager unlocked successfully!");
                    } else {
                        ShowNotification("Invalid master password!", true);
                    }
                }
                
            } else {
                ImGui::Text("Set up a master password:");
                ImGui::InputText("New Password", m_newMasterPasswordBuffer, sizeof(m_newMasterPasswordBuffer),
                               ImGuiInputTextFlags_Password);
                ImGui::InputText("Confirm Password", m_confirmPasswordBuffer, sizeof(m_confirmPasswordBuffer),
                               ImGuiInputTextFlags_Password);
                
                if (ImGui::Button("Set Master Password")) {
                    if (strlen(m_newMasterPasswordBuffer) >= Config::MIN_MASTER_PASSWORD_LENGTH) {
                        if (strcmp(m_newMasterPasswordBuffer, m_confirmPasswordBuffer) == 0) {
                            if (m_passwordManager->SetMasterPassword(m_newMasterPasswordBuffer)) {
                                m_showMasterPasswordDialog = false;
                                memset(m_newMasterPasswordBuffer, 0, sizeof(m_newMasterPasswordBuffer));
                                memset(m_confirmPasswordBuffer, 0, sizeof(m_confirmPasswordBuffer));
                                ShowNotification("Master password set successfully!");
                            } else {
                                ShowNotification("Failed to set master password!", true);
                            }
                        } else {
                            ShowNotification("Passwords do not match!", true);
                        }
                    } else {
                        ShowNotification("Password must be at least 8 characters long!", true);
                    }
                }
            }
            
            ImGui::SameLine();
            if (ImGui::Button("Cancel")) {
                m_showMasterPasswordDialog = false;
                memset(m_masterPasswordBuffer, 0, sizeof(m_masterPasswordBuffer));
                memset(m_newMasterPasswordBuffer, 0, sizeof(m_newMasterPasswordBuffer));
                memset(m_confirmPasswordBuffer, 0, sizeof(m_confirmPasswordBuffer));
            }
            
            ImGui::EndPopup();
        }
    }

    void ToolboxApp::RenderAboutDialog() {
        ImGui::OpenPopup("About Toolbox");
        
        ImVec2 center = ImGui::GetMainViewport()->GetCenter();
        ImGui::SetNextWindowPos(center, ImGuiCond_Appearing, ImVec2(0.5f, 0.5f));
        
        if (ImGui::BeginPopupModal("About Toolbox", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
            ImGui::Text("Toolbox v%s", Config::APP_VERSION);
            ImGui::Separator();
            ImGui::Text("A comprehensive utility application featuring:");
            ImGui::BulletText("Secure Password Manager with AES-256 encryption");
            ImGui::BulletText("Scientific Calculator with expression evaluation");
            ImGui::BulletText("Advanced File Search for text files");
            ImGui::Separator();
            ImGui::Text("Built with OpenGL, Dear ImGui, and modern C++");
            
            if (ImGui::Button("Close")) {
                m_showAboutDialog = false;
            }
            
            ImGui::EndPopup();
        }
    }

    void ToolboxApp::OnFramebufferSize(int width, int height) {
        glViewport(0, 0, width, height);
    }

    void ToolboxApp::OnKey(int key, int scancode, int action, int mods) {
        // Handle global key shortcuts
        if (action == GLFW_PRESS) {
            if (mods & GLFW_MOD_ALT) {
                if (key == GLFW_KEY_F4) {
                    m_isRunning = false;
                }
            }
        }
    }

    void ToolboxApp::OnMouseButton(int button, int action, int mods) {
        // Handle mouse events if needed
    }

    void ToolboxApp::SetApplicationMode(ApplicationMode mode) {
        m_currentMode = mode;
    }

    void ToolboxApp::ShowNotification(const std::string& message, bool isError) {
        // For now, just print to console
        // In a full implementation, this would show a toast notification
        if (isError) {
            std::cerr << "Error: " << message << std::endl;
        } else {
            std::cout << "Info: " << message << std::endl;
        }
    }

    void ToolboxApp::CreateDataDirectory() {
        std::filesystem::create_directories(Config::DATA_DIRECTORY);
    }

    void ToolboxApp::SaveApplicationState() {
        // Save password data if unlocked
        if (!m_passwordManager->IsLocked()) {
            m_passwordManager->SaveToFile(Config::PASSWORD_DATA_FILE);
        }
    }

    void ToolboxApp::LoadApplicationState() {
        // Application state loading is handled in InitializeComponents
    }
}

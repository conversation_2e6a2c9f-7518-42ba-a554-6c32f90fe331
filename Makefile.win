# Windows-specific Makefile for Toolbox Application
# Use with: make -f Makefile.win

PROJECT_NAME = Toolbox
BUILD_DIR = build
SRC_DIR = src
INCLUDE_DIR = include
EXTERNAL_DIR = external
DESKTOP_DIR = C:\Users\<USER>\Desktop

# Detect compiler
!IF "$(CXX)" == ""
CXX = g++
!ENDIF

# Build configuration
BUILD_TYPE = Release
TARGET = $(DESKTOP_DIR)\$(PROJECT_NAME).exe
BUILD_TARGET = $(BUILD_DIR)\bin\$(PROJECT_NAME).exe

# Source files
SOURCES = $(SRC_DIR)\main.cpp $(SRC_DIR)\Toolbox.cpp $(SRC_DIR)\Window.cpp \
          $(SRC_DIR)\PasswordManager.cpp $(SRC_DIR)\Encryption.cpp \
          $(SRC_DIR)\Calculator.cpp $(SRC_DIR)\FileSearcher.cpp $(SRC_DIR)\Utils.cpp

# Include directories
INCLUDES = -I$(INCLUDE_DIR) -I$(EXTERNAL_DIR)\glfw\include -I$(EXTERNAL_DIR)\glad\include \
           -I$(EXTERNAL_DIR)\imgui -I$(EXTERNAL_DIR)\imgui\backends -I$(EXTERNAL_DIR)\stb

# Compiler flags
CXXFLAGS = -std=c++17 -Wall -Wextra $(INCLUDES) -O3 -DNDEBUG

# Libraries
LIBS = -lopengl32 -lgdi32 -luser32 -lkernel32 -lshell32 -lole32 \
       -loleaut32 -luuid -ladvapi32 -lcrypt32 -lbcrypt

# External objects
EXTERNAL_OBJS = $(BUILD_DIR)\obj\glad.o $(BUILD_DIR)\obj\imgui.o \
                $(BUILD_DIR)\obj\imgui_demo.o $(BUILD_DIR)\obj\imgui_draw.o \
                $(BUILD_DIR)\obj\imgui_tables.o $(BUILD_DIR)\obj\imgui_widgets.o \
                $(BUILD_DIR)\obj\imgui_impl_glfw.o $(BUILD_DIR)\obj\imgui_impl_opengl3.o \
                $(BUILD_DIR)\obj\stb_image.o

# Object files
OBJECTS = $(BUILD_DIR)\obj\main.o $(BUILD_DIR)\obj\Toolbox.o $(BUILD_DIR)\obj\Window.o \
          $(BUILD_DIR)\obj\PasswordManager.o $(BUILD_DIR)\obj\Encryption.o \
          $(BUILD_DIR)\obj\Calculator.o $(BUILD_DIR)\obj\FileSearcher.o $(BUILD_DIR)\obj\Utils.o

# GLFW library
GLFW_LIB = $(EXTERNAL_DIR)\glfw\build\src\libglfw3.a

# Default target
all: setup-dirs $(TARGET)

# Setup directories
setup-dirs:
	@if not exist "$(BUILD_DIR)" mkdir "$(BUILD_DIR)"
	@if not exist "$(BUILD_DIR)\obj" mkdir "$(BUILD_DIR)\obj"
	@if not exist "$(BUILD_DIR)\bin" mkdir "$(BUILD_DIR)\bin"
	@if not exist "data" mkdir "data"

# Main target
$(TARGET): $(OBJECTS) $(EXTERNAL_OBJS) $(GLFW_LIB)
	@echo Linking $(PROJECT_NAME)...
	@$(CXX) $(OBJECTS) $(EXTERNAL_OBJS) $(GLFW_LIB) $(LIBS) -static-libgcc -static-libstdc++ -o $(BUILD_TARGET)
	@echo Copying to Desktop...
	@copy "$(BUILD_TARGET)" "$(TARGET)"
	@copy "image2vector.svg" "$(DESKTOP_DIR)\"
	@echo Build completed: $(TARGET)

# Compile source files
$(BUILD_DIR)\obj\main.o: $(SRC_DIR)\main.cpp
	@echo Compiling main.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\main.cpp -o $(BUILD_DIR)\obj\main.o

$(BUILD_DIR)\obj\Toolbox.o: $(SRC_DIR)\Toolbox.cpp
	@echo Compiling Toolbox.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\Toolbox.cpp -o $(BUILD_DIR)\obj\Toolbox.o

$(BUILD_DIR)\obj\Window.o: $(SRC_DIR)\Window.cpp
	@echo Compiling Window.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\Window.cpp -o $(BUILD_DIR)\obj\Window.o

$(BUILD_DIR)\obj\PasswordManager.o: $(SRC_DIR)\PasswordManager.cpp
	@echo Compiling PasswordManager.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\PasswordManager.cpp -o $(BUILD_DIR)\obj\PasswordManager.o

$(BUILD_DIR)\obj\Encryption.o: $(SRC_DIR)\Encryption.cpp
	@echo Compiling Encryption.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\Encryption.cpp -o $(BUILD_DIR)\obj\Encryption.o

$(BUILD_DIR)\obj\Calculator.o: $(SRC_DIR)\Calculator.cpp
	@echo Compiling Calculator.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\Calculator.cpp -o $(BUILD_DIR)\obj\Calculator.o

$(BUILD_DIR)\obj\FileSearcher.o: $(SRC_DIR)\FileSearcher.cpp
	@echo Compiling FileSearcher.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\FileSearcher.cpp -o $(BUILD_DIR)\obj\FileSearcher.o

$(BUILD_DIR)\obj\Utils.o: $(SRC_DIR)\Utils.cpp
	@echo Compiling Utils.cpp...
	@$(CXX) $(CXXFLAGS) -c $(SRC_DIR)\Utils.cpp -o $(BUILD_DIR)\obj\Utils.o

# External libraries
$(BUILD_DIR)\obj\glad.o: $(EXTERNAL_DIR)\glad\src\glad.c
	@echo Compiling GLAD...
	@gcc $(CXXFLAGS) -c $(EXTERNAL_DIR)\glad\src\glad.c -o $(BUILD_DIR)\obj\glad.o

$(BUILD_DIR)\obj\imgui.o: $(EXTERNAL_DIR)\imgui\imgui.cpp
	@echo Compiling ImGui...
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\imgui.cpp -o $(BUILD_DIR)\obj\imgui.o

$(BUILD_DIR)\obj\imgui_demo.o: $(EXTERNAL_DIR)\imgui\imgui_demo.cpp
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\imgui_demo.cpp -o $(BUILD_DIR)\obj\imgui_demo.o

$(BUILD_DIR)\obj\imgui_draw.o: $(EXTERNAL_DIR)\imgui\imgui_draw.cpp
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\imgui_draw.cpp -o $(BUILD_DIR)\obj\imgui_draw.o

$(BUILD_DIR)\obj\imgui_tables.o: $(EXTERNAL_DIR)\imgui\imgui_tables.cpp
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\imgui_tables.cpp -o $(BUILD_DIR)\obj\imgui_tables.o

$(BUILD_DIR)\obj\imgui_widgets.o: $(EXTERNAL_DIR)\imgui\imgui_widgets.cpp
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\imgui_widgets.cpp -o $(BUILD_DIR)\obj\imgui_widgets.o

$(BUILD_DIR)\obj\imgui_impl_glfw.o: $(EXTERNAL_DIR)\imgui\backends\imgui_impl_glfw.cpp
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\backends\imgui_impl_glfw.cpp -o $(BUILD_DIR)\obj\imgui_impl_glfw.o

$(BUILD_DIR)\obj\imgui_impl_opengl3.o: $(EXTERNAL_DIR)\imgui\backends\imgui_impl_opengl3.cpp
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\imgui\backends\imgui_impl_opengl3.cpp -o $(BUILD_DIR)\obj\imgui_impl_opengl3.o

$(BUILD_DIR)\obj\stb_image.o: $(EXTERNAL_DIR)\stb\stb_image.cpp
	@echo Compiling STB Image...
	@$(CXX) $(CXXFLAGS) -c $(EXTERNAL_DIR)\stb\stb_image.cpp -o $(BUILD_DIR)\obj\stb_image.o

# GLFW
$(GLFW_LIB):
	@echo Building GLFW...
	@if not exist "$(EXTERNAL_DIR)\glfw\build" mkdir "$(EXTERNAL_DIR)\glfw\build"
	@cd $(EXTERNAL_DIR)\glfw\build && cmake .. -G "MinGW Makefiles" -DGLFW_BUILD_DOCS=OFF -DGLFW_BUILD_TESTS=OFF -DGLFW_BUILD_EXAMPLES=OFF
	@cd $(EXTERNAL_DIR)\glfw\build && mingw32-make

# Clean
clean:
	@echo Cleaning build files...
	@if exist "$(BUILD_DIR)" rmdir /s /q "$(BUILD_DIR)"

# Run
run: $(TARGET)
	@echo Running $(PROJECT_NAME)...
	@cd $(DESKTOP_DIR) && $(PROJECT_NAME).exe

.PHONY: all setup-dirs clean run

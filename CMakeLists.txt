cmake_minimum_required(VERSION 3.16)
project(Toolbox)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${C<PERSON><PERSON>_BINARY_DIR}/bin)

# Find packages
find_package(OpenGL REQUIRED)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/external/glfw/include)
include_directories(${CMAKE_SOURCE_DIR}/external/glad/include)
include_directories(${CMAKE_SOURCE_DIR}/external/imgui)
include_directories(${CMAKE_SOURCE_DIR}/external/stb)

# GLFW
set(GLFW_BUILD_DOCS OFF CACHE BOOL "" FORCE)
set(GLFW_BUILD_TESTS OFF CACHE BOOL "" FORCE)
set(GLFW_BUILD_EXAMPLES OFF CACHE BOOL "" FORCE)
add_subdirectory(external/glfw)

# GLAD
add_library(glad external/glad/src/glad.c)
target_include_directories(glad PUBLIC external/glad/include)

# ImGui
set(IMGUI_SOURCES
    external/imgui/imgui.cpp
    external/imgui/imgui_demo.cpp
    external/imgui/imgui_draw.cpp
    external/imgui/imgui_tables.cpp
    external/imgui/imgui_widgets.cpp
    external/imgui/backends/imgui_impl_glfw.cpp
    external/imgui/backends/imgui_impl_opengl3.cpp
)
add_library(imgui ${IMGUI_SOURCES})
target_include_directories(imgui PUBLIC external/imgui)
target_link_libraries(imgui glfw glad)

# STB Image
add_library(stb_image external/stb/stb_image.cpp)
target_include_directories(stb_image PUBLIC external/stb)

# Source files
set(SOURCES
    src/main.cpp
    src/Toolbox.cpp
    src/Window.cpp
    src/PasswordManager.cpp
    src/Encryption.cpp
    src/MasterPassword.cpp
    src/Calculator.cpp
    src/CalculatorUI.cpp
    src/FileSearcher.cpp
    src/SearchUI.cpp
    src/UIManager.cpp
    src/Renderer.cpp
    src/Texture.cpp
    src/Utils.cpp
)

# Create executable
add_executable(Toolbox ${SOURCES})

# Link libraries
target_link_libraries(Toolbox 
    OpenGL::GL 
    glfw 
    glad 
    imgui 
    stb_image
    crypt32  # Windows crypto API
    bcrypt   # Windows crypto API
)

# Set executable name
set_target_properties(Toolbox PROPERTIES OUTPUT_NAME "Toolbox")

# Copy resources
file(COPY ${CMAKE_SOURCE_DIR}/resources DESTINATION ${CMAKE_BINARY_DIR}/bin)

#pragma once

#include "Window.h"
#include "PasswordManager.h"
#include "Calculator.h"
#include "FileSearcher.h"
#include <memory>
#include <string>

namespace Toolbox {
    enum class ApplicationMode {
        PasswordManager,
        Calculator,
        FileSearch
    };

    class ToolboxApp {
    public:
        ToolboxApp();
        ~ToolboxApp();

        bool Initialize();
        void Run();
        void Shutdown();

    private:
        // Core components
        std::unique_ptr<Window> m_window;
        std::unique_ptr<PasswordManager> m_passwordManager;
        std::unique_ptr<Calculator> m_calculator;
        std::unique_ptr<FileSearcher> m_fileSearcher;
        
        // Application state
        ApplicationMode m_currentMode;
        bool m_isRunning;
        bool m_showMasterPasswordDialog;
        bool m_showAboutDialog;
        
        // UI state
        char m_masterPasswordBuffer[256];
        char m_newMasterPasswordBuffer[256];
        char m_confirmPasswordBuffer[256];
        bool m_masterPasswordVisible;
        
        // Initialization
        bool InitializeWindow();
        bool InitializeImGui();
        bool InitializeComponents();
        void LoadApplicationIcon();
        
        // Main loop
        void Update();
        void Render();
        
        // UI rendering
        void RenderMainMenuBar();
        void RenderSidebar();
        void RenderMainContent();
        void RenderStatusBar();
        
        // Mode-specific rendering
        void RenderPasswordManager();
        void RenderCalculator();
        void RenderFileSearch();
        
        // Dialog rendering
        void RenderMasterPasswordDialog();
        void RenderAboutDialog();
        
        // Event handling
        void OnFramebufferSize(int width, int height);
        void OnKey(int key, int scancode, int action, int mods);
        void OnMouseButton(int button, int action, int mods);
        
        // Utility functions
        void SetApplicationMode(ApplicationMode mode);
        void ShowNotification(const std::string& message, bool isError = false);
        void CreateDataDirectory();
        
        // Application lifecycle
        void SaveApplicationState();
        void LoadApplicationState();
    };
}

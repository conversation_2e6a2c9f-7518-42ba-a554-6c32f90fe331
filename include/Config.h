#pragma once

#include <string>

namespace Toolbox {
    namespace Config {
        // Application info
        constexpr const char* APP_NAME = "Toolbox";
        constexpr const char* APP_VERSION = "1.0.0";
        constexpr int WINDOW_WIDTH = 1200;
        constexpr int WINDOW_HEIGHT = 800;
        constexpr int MIN_WINDOW_WIDTH = 800;
        constexpr int MIN_WINDOW_HEIGHT = 600;

        // File paths
        constexpr const char* COVER_IMAGE_PATH = "image2vector.svg";
        constexpr const char* PASSWORD_DATA_FILE = "data/passwords.dat";
        constexpr const char* CONFIG_FILE = "data/config.ini";
        constexpr const char* DATA_DIRECTORY = "data";

        // UI Constants
        constexpr float SIDEBAR_WIDTH = 200.0f;
        constexpr float TOOLBAR_HEIGHT = 50.0f;
        constexpr float PADDING = 10.0f;

        // Security
        constexpr int MIN_MASTER_PASSWORD_LENGTH = 8;
        constexpr int MAX_PASSWORD_ENTRY_LENGTH = 256;
        constexpr int ENCRYPTION_KEY_SIZE = 32; // AES-256
        constexpr int ENCRYPTION_IV_SIZE = 16;

        // Calculator
        constexpr int MAX_CALCULATOR_HISTORY = 100;
        constexpr int MAX_DISPLAY_DIGITS = 15;

        // File Search
        constexpr int MAX_SEARCH_RESULTS = 1000;
        constexpr int MAX_FILE_PREVIEW_SIZE = 1024; // bytes
        
        // Colors (RGBA)
        constexpr float BACKGROUND_COLOR[4] = {0.1f, 0.1f, 0.1f, 1.0f};
        constexpr float SIDEBAR_COLOR[4] = {0.15f, 0.15f, 0.15f, 1.0f};
        constexpr float ACCENT_COLOR[4] = {0.2f, 0.6f, 0.9f, 1.0f};
        constexpr float TEXT_COLOR[4] = {0.9f, 0.9f, 0.9f, 1.0f};
        constexpr float ERROR_COLOR[4] = {0.9f, 0.2f, 0.2f, 1.0f};
        constexpr float SUCCESS_COLOR[4] = {0.2f, 0.9f, 0.2f, 1.0f};
    }
}

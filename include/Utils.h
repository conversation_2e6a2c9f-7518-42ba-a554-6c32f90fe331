#pragma once

#include <string>
#include <vector>
#include <chrono>

namespace Toolbox {
    namespace Utils {
        // String utilities
        std::string Trim(const std::string& str);
        std::string ToLower(const std::string& str);
        std::string ToUpper(const std::string& str);
        std::vector<std::string> Split(const std::string& str, char delimiter);
        std::string Join(const std::vector<std::string>& strings, const std::string& delimiter);
        bool StartsWith(const std::string& str, const std::string& prefix);
        bool EndsWith(const std::string& str, const std::string& suffix);
        std::string Replace(const std::string& str, const std::string& from, const std::string& to);
        
        // File utilities
        bool FileExists(const std::string& path);
        bool DirectoryExists(const std::string& path);
        bool CreateDirectory(const std::string& path);
        std::string GetExecutableDirectory();
        std::string GetUserDocumentsDirectory();
        std::string GetTempDirectory();
        std::string CombinePaths(const std::string& path1, const std::string& path2);
        
        // Time utilities
        std::string GetCurrentTimeString();
        std::string FormatTimestamp(long long timestamp);
        long long GetCurrentTimestamp();
        
        // System utilities
        void OpenURL(const std::string& url);
        void OpenFileExplorer(const std::string& path);
        std::string GetSystemInfo();
        
        // Clipboard utilities
        bool SetClipboardText(const std::string& text);
        std::string GetClipboardText();
        
        // Validation utilities
        bool IsValidEmail(const std::string& email);
        bool IsValidURL(const std::string& url);
        bool IsNumeric(const std::string& str);
        
        // Encoding utilities
        std::string Base64Encode(const std::vector<uint8_t>& data);
        std::vector<uint8_t> Base64Decode(const std::string& encoded);
        std::string URLEncode(const std::string& str);
        std::string URLDecode(const std::string& str);
        
        // Random utilities
        std::string GenerateUUID();
        int GenerateRandomInt(int min, int max);
        double GenerateRandomDouble(double min, double max);
        
        // Memory utilities
        void SecureZeroMemory(void* ptr, size_t size);
        
        // Configuration utilities
        bool SaveConfigValue(const std::string& key, const std::string& value);
        std::string LoadConfigValue(const std::string& key, const std::string& defaultValue = "");
        
        // Error handling utilities
        std::string GetLastErrorString();
        void LogError(const std::string& message);
        void LogInfo(const std::string& message);
    }
}

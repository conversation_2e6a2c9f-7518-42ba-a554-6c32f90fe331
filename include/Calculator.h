#pragma once

#include <string>
#include <vector>
#include <stack>
#include <deque>

namespace Toolbox {
    enum class CalculatorMode {
        Basic,
        Scientific,
        Programmer
    };

    struct CalculationHistory {
        std::string expression;
        std::string result;
        long long timestamp;
        
        CalculationHistory(const std::string& expr, const std::string& res)
            : expression(expr), result(res), timestamp(0) {}
    };

    class Calculator {
    public:
        Calculator();
        ~Calculator();

        // Basic operations
        std::string Calculate(const std::string& expression);
        void Clear();
        void ClearHistory();
        
        // Input handling
        void AppendDigit(char digit);
        void AppendOperator(const std::string& op);
        void AppendFunction(const std::string& func);
        void Backspace();
        void SetDecimalPoint();
        
        // Memory operations
        void MemoryStore();
        void MemoryRecall();
        void MemoryAdd();
        void MemorySubtract();
        void MemoryClear();
        
        // Mode and settings
        void SetMode(CalculatorMode mode);
        CalculatorMode GetMode() const { return m_mode; }
        
        void SetAngleUnit(bool radians) { m_useRadians = radians; }
        bool IsUsingRadians() const { return m_useRadians; }
        
        // Display and history
        std::string GetCurrentExpression() const { return m_currentExpression; }
        std::string GetCurrentResult() const { return m_currentResult; }
        std::vector<CalculationHistory> GetHistory() const { return m_history; }
        
        // Error handling
        bool HasError() const { return m_hasError; }
        std::string GetErrorMessage() const { return m_errorMessage; }
        
        // Constants
        static constexpr double PI = 3.14159265358979323846;
        static constexpr double E = 2.71828182845904523536;
        
    private:
        CalculatorMode m_mode;
        std::string m_currentExpression;
        std::string m_currentResult;
        double m_memoryValue;
        bool m_useRadians;
        bool m_hasError;
        std::string m_errorMessage;
        std::deque<CalculationHistory> m_history;
        
        // Expression evaluation
        double EvaluateExpression(const std::string& expression);
        std::vector<std::string> TokenizeExpression(const std::string& expression);
        std::vector<std::string> ConvertToPostfix(const std::vector<std::string>& tokens);
        double EvaluatePostfix(const std::vector<std::string>& postfix);
        
        // Helper functions
        bool IsOperator(const std::string& token) const;
        bool IsFunction(const std::string& token) const;
        int GetOperatorPrecedence(const std::string& op) const;
        bool IsLeftAssociative(const std::string& op) const;
        double ApplyOperator(const std::string& op, double a, double b);
        double ApplyFunction(const std::string& func, double value);
        
        // Utility functions
        std::string FormatNumber(double value) const;
        void SetError(const std::string& message);
        void ClearError();
        long long GetCurrentTimestamp() const;
        
        // Number base conversions (for programmer mode)
        std::string DecimalToBinary(long long value) const;
        std::string DecimalToHex(long long value) const;
        std::string DecimalToOctal(long long value) const;
        long long BinaryToDecimal(const std::string& binary) const;
        long long HexToDecimal(const std::string& hex) const;
        long long OctalToDecimal(const std::string& octal) const;
    };
}

dear imgui
ISSUES & TODO LIST

Issue numbers (#) refer to GitHub issues listed at https://github.com/ocornut/imgui/issues/XXXX
THIS LIST IS NOT WELL MAINTAINED. MOST OF THE WORK HAPPENS ON GITHUB NOWADAYS.
The list below consist mostly of ideas noted down before they are requested/discussed by users (at which point they usually exist on the github issue tracker).
It's mostly a bunch of personal notes, probably incomplete. Feel free to query if you have any questions.

 - doc: add a proper documentation system (maybe relying on automation? #435)
 - doc: checklist app to verify backends/integration of imgui (test inputs, rendering, callback, etc.).
 - doc/tips: tips of the day: website? applet in imgui_club?
 - doc/wiki: work on the wiki https://github.com/ocornut/imgui/wiki

 - window: preserve/restore relative focus ordering (persistent or not), and e.g. of multiple reappearing windows (#2304) -> also see docking reference to same #.
 - window: calling SetNextWindowSize() every frame with <= 0 doesn't do anything, may be useful to allow (particularly when used for a single axis). (#690)
 - window: add a way for very transient windows (non-saved, temporary overlay over hundreds of objects) to "clean" up from the global window list. perhaps a lightweight explicit cleanup pass.
 - window: auto-fit feedback loop when user relies on any dynamic layout (window width multiplier, column) appears weird to end-user. clarify.
 - window: begin with *p_open == false could return false.
 - window: get size/pos helpers given names (see discussion in #249)
 - window: when window is very small, prioritize resize button over close button.
 - window: double-clicking on title bar to minimize isn't consistent interaction, perhaps move to single-click on left-most collapse icon?
 - window: expose contents size. (#1045)
 - window: using SetWindowPos() inside Begin() and moving the window with the mouse reacts a very ugly glitch. We should just defer the SetWindowPos() call.
 - window: GetWindowSize() returns (0,0) when not calculated? (#1045)
 - window: investigate better auto-positioning for new windows.
 - window: top most window flag? more z-order contrl? (#2574)
 - window/size: manually triggered auto-fit (double-click on grip) shouldn't resize window down to viewport size?
 - window/size: how to allow to e.g. auto-size vertically to fit contents, but be horizontally resizable? Assuming SetNextWindowSize() is modified to treat -1.0f on each axis as "keep as-is" (would be good but might break erroneous code): Problem is UpdateWindowManualResize() and lots of code treat (window->AutoFitFramesX > 0 || window->AutoFitFramesY > 0) together.
 - window/opt: freeze window flag: if not focused/hovered, return false, render with previous ImDrawList. and/or reduce refresh rate. -> this may require enforcing that it is illegal to submit contents if Begin returns false.
 - window/child: background options for child windows, border option (disable rounding).
 - window/child: allow resizing of child windows (possibly given min/max for each axis?.)
 - window/child: allow SetNextWindowContentSize() to work on child windows.
 - window/clipping: some form of clipping when DisplaySize (or corresponding viewport) is zero.
 - window/tabbing: add a way to signify that a window or docked window requires attention (e.g. blinking title bar, trying to click behind a modal).
 - window/id_stack: add e.g. window->GetIDFromPath() with support for leading / and ../ (#1390, #331) -> model from test engine.
 ! scrolling: exposing horizontal scrolling with Shift+Wheel even when scrollbar is disabled expose lots of issues (#2424, #1463)
 - scrolling: while holding down a scrollbar, try to keep the same contents visible (at least while not moving mouse)
 - scrolling: allow immediately effective change of scroll after Begin() if we haven't appended items yet.
 - scrolling: forward mouse wheel scrolling to parent window when at the edge of scrolling limits? (useful for listbox,tables?)
 - scrolling/style: shadows on scrollable areas to denote that there is more contents (see e.g. DaVinci Resolve ui)

 - drawdata: make it easy to deep-copy (or swap?) a full ImDrawData so user can easily save that data if they use threaded rendering. (e.g. #2646)
 ! drawlist: add CalcTextSize() func to facilitate consistent code from user pov (currently need to use ImGui or ImFont alternatives!)
 - drawlist: maintaining bounding box per command would allow to merge draw command when clipping isn't relied on (typical non-scrolling window or non-overflowing column would merge with previous command). (WIP branch)
 - drawlist: make it easier to toggle AA per primitive, so we can use e.g. non-AA fill + AA borders more naturally
 - drawlist: non-AA strokes have gaps between points (#593, #288), glitch especially on RenderCheckmark() and ColorPicker4().
 - drawlist: callback: add an extra void* in ImDrawCallback to allow passing render-local data to the callback (would break API).
 - drawlist: AddRect vs AddLine position confusing (#2441)
 - drawlist/opt: store rounded corners in texture to use 1 quad per corner (filled and wireframe) to lower the cost of rounding. (#1962)
 - drawlist/opt: AddRect() axis aligned pixel aligned (no-aa) could use 8 triangles instead of 16 and no normal calculation.
 - drawlist/opt: thick AA line could be doable in same number of triangles as 1.0 AA line by storing gradient+full color in atlas.

 - items: IsItemHovered() info stored in a stack? so that 'if TreeNode() { Text; TreePop; } if IsHovered' return the hover state of the TreeNode?

 - widgets: display mode: widget-label, label-widget (aligned on column or using fixed size), label-newline-tab-widget etc. (#395)
 - widgets: clean up widgets internal toward exposing everything and stabilizing imgui_internals.h.
 - widgets: add always-allow-overlap mode. This should perhaps be the default? one problem is that highlight after mouse-wheel scrolling gets deferred, makes scrolling more flickery.
 - widgets: start exposing PushItemFlag() and ImGuiItemFlags
 - widgets: alignment options in style (e.g. center Selectable, Right-Align within Button, etc.) #1260
 - widgets: activate by identifier (trigger button, focus given id)
 - widgets: custom glyph/shapes replacements for stock sapes. (also #6090 #2431 #2235 #6517)
 - widgets: coloredit: keep reporting as active when picker is on?
 - widgets: group/scalarn functions: expose more per-component information. e.g. store NextItemData.ComponentIdx set by scalarn function, groups can expose them back somehow.
 - selectable: using (size.x == 0.0f) and (SelectableTextAlign.x > 0.0f) followed by SameLine() is currently not supported.
 - selectable: generic BeginSelectable()/EndSelectable() mechanism. (work out alongside range-select branch)
 - selectable: a way to visualize partial/mixed selection (e.g. parent tree node has children with mixed selection)

 - input text: clean up the mess caused by converting UTF-8 <> wchar. the code is rather inefficient right now and super fragile. (WIP branch)
 - input text: preserve scrolling when unfocused?
 - input text: reorganize event handling, allow CharFilter to modify buffers, allow multiple events? (#541)
 - input text: expose CursorPos in char filter event (#816)
 - input text: try usage idiom of using InputText with data only exposed through get/set accessors, without extraneous copy/alloc. (#3009)
 - input text: access public fields via a non-callback API e.g. InputTextGetState("xxx") that may return nullptr if not active (available in internals)
 - input text: flag to disable live update of the user buffer (also applies to float/int text input) (#701)
 - input text: hover tooltip could show unclamped text
 - input text: support for INSERT key to toggle overwrite mode. currently disabled because stb_textedit behavior is unsatisfactory on multi-line. (#2863)
 - input text: option to Tab after an Enter validation.
 - input text: add ImGuiInputTextFlags_EnterToApply? (off #218)
 - input text: easier ways to update buffer (from source char*) while owned. preserve some sort of cursor position for multi-line text.
 - input text: add discard flag (e.g. ImGuiInputTextFlags_DiscardActiveBuffer) or make it easier to clear active focus for text replacement during edition (#725)
 - input text: display bug when clicking a drag/slider after an input text in a different window has all-selected text (order dependent). actually a very old bug but no one appears to have noticed it.
 - input text: allow centering/positioning text so that ctrl+clicking Drag or Slider keeps the textual value at the same pixel position.
 - input text: decorrelate display layout from inputs with custom template - e.g. what's the easiest way to implement a nice IP/Mac address input editor?
 - input text: global callback system so user can plug in an expression evaluator easily. (#1691)
 - input text: force scroll to end or scroll to a given line/contents (so user can implement a log or a search feature)
 - input text: a way to preview completion (e.g. disabled text completing from the cursor)
 - input text: a side bar that could e.g. preview where errors are. probably left to the user to draw but we'd need to give them the info there.
 - input text: a way for the user to provide syntax coloring.
 - input text: Shift+TAB with ImGuiInputTextFlags_AllowTabInput could eat preceding blanks, up to tab_count.
 - input text multi-line: don't directly call AddText() which does an unnecessary vertex reserve for character count prior to clipping. and/or more line-based clipping to AddText(). and/or reorganize TextUnformatted/RenderText for more efficiency for large text (e.g TextUnformatted could clip and log separately, etc).
 - input text multi-line: support for copy/cut without selection (copy/cut current line?)
 - input text multi-line: line numbers? status bar? (follow up on #200)
 - input text multi-line: behave better when user changes input buffer while editing is active (even though it is illegal behavior). namely, the change of buffer can create a scrollbar glitch (#725)
 - input text multi-line: better horizontal scrolling support (#383, #1224)
 - input text multi-line: single call to AddText() should be coarse clipped on InputTextEx() end.
 - input number: optional range min/max for Input*() functions
 - input number: holding [-]/[+] buttons could increase the step speed non-linearly (or user-controlled)
 - input number: use mouse wheel to step up/down

 - layout: helper or a way to express ImGui::SameLine(ImGui::GetCursorStartPos().x + ImGui::CalcItemWidth() + ImGui::GetStyle().ItemInnerSpacing.x); in a simpler manner.
 - layout, font: horizontal tab support, A) text mode: forward only tabs (e.g. every 4 characters/N pixels from pos x1), B) manual mode: explicit tab stops acting as mini columns, no clipping (for menu items, many kind of uses, also vaguely relate to #267, #395)
 - layout: horizontal layout helper (#97)
 - layout: horizontal flow until no space left (#404)
 - layout: more generic alignment state (left/right/centered) for single items?
 - layout: clean up the InputFloatN/SliderFloatN/ColorEdit4 layout code. item width should include frame padding.
 - layout: vertical alignment of mixed height items (e.g. buttons) within a same line (#1284)
 - layout: null layout mode were items are not rendered but user can query GetItemRectMin()/Max/Size.
 - layout: (R&D) local multi-pass layout mode.
 - layout: (R&D) bind authored layout data (created by an off-line tool), items fetch their pos/size at submission, self-optimize data structures to stable linear access.

 - tables: see https://github.com/ocornut/imgui/issues/2957#issuecomment-569726095

 - group: BeginGroup() needs a border option. (~#1496)
 - group: IsItemHovered() after EndGroup() covers whole AABB rather than the intersection of individual items. Is that desirable?
 - group: merge deactivation/activation within same group (fwd WasEdited flag). (#2550)

!- color: the color conversion helpers/types are a mess and needs sorting out.
 - color: (api breaking) ImGui::ColorConvertXXX functions should be loose ImColorConvertXX to match imgui_internals.h

 - plot: full featured plot/graph api w/ scrolling, zooming etc. --> ImPlot
 - (plot: deleted all other todo lines on 2023-06-28)

 - clipper: ability to disable the clipping through a simple flag/bool.
 - clipper: ability to run without knowing full count in advance.
 - clipper: horizontal clipping support. (#2580)

 - separator: expose flags (#759)
 - separator: take indent into consideration (optional)
 - separator: width, thickness, centering (#1643)
 - splitter: formalize the splitter idiom into an official api (we want to handle n-way split) (#319)

 - docking: merge docking branch (#2109)

 - tabs: "there is currently a problem because TabItem() will try to submit their own tooltip after 0.50 second, and this will have the effect of making your tooltip flicker once." -> tooltip priority work (WIP branch)
 - tabs: make EndTabBar fail if users doesn't respect BeginTabBar return value, for consistency/future-proofing.
 - tabs: persistent order/focus in BeginTabBar() api (#261, #351)
 - tabs: explicit api (even if internal) to cleanly manipulate tab order.

 - image/image button: misalignment on padded/bordered button?
 - image/image button: parameters are confusing, image() has tint_col,border_col whereas imagebutton() has bg_col/tint_col. Even thou they are different parameters ordering could be more consistent. can we fix that?
 - slider: allow using the [-]/[+] buttons used by InputFloat()/InputInt()
 - slider: add dragging-based widgets to edit values with mouse (on 2 axises), saving screen real-estate.
 - slider: tint background based on value (e.g. v_min -> v_max, or use 0.0f either side of the sign)
 - slider: relative dragging? + precision dragging
 - slider: step option (#1183)
 - slider: style: fill % of the bar instead of positioning a drag.
 - knob: rotating knob widget (#942)
 - drag float: support for reversed drags (min > max) (removed is_locked, also see fdc526e)
 - drag float: up/down axis
 - drag float: power != 0.0f with current value being outside the range keeps the value stuck.
 - drag float: added leeway on edge (e.g. a few invisible steps past the clamp limits)

 - combo: use clipper.
 - combo: a way/helper to customize the combo preview (#1658) -> experimental BeginComboPreview()
 - combo/listbox: keyboard control. need InputText-like non-active focus + key handling. considering keyboard for custom listbox (pr #203)
 - listbox: multiple selection (WIP range-select branch)
 - listbox: unselect option (#1208)
 - listbox: make it easier/more natural to implement range-select (need some sort of info/ref about the last clicked/focused item that user can translate to an index?) (WIP range-select branch)
 - listbox: user may want to initial scroll to focus on the one selected value?
 - listbox: disable capturing mouse wheel if the listbox has no scrolling. (#1681)
 - listbox: scrolling should track modified selection.
 - listbox: future api should allow to enable horizontal scrolling (#2510)

!- popups/menus: clarify usage of popups id, how MenuItem/Selectable closing parent popups affects the ID, etc. this is quite fishy needs improvement! (#331, #402)
 - modals: make modal title bar blink when trying to click outside the modal
 - modals: technically speaking, we could make Begin() with ImGuiWindowFlags_Modal work without involving popup. May help untangle a few things, as modals are more like regular windows than popups.
 - popups: if the popup functions took explicit ImGuiID it would allow the user to manage the scope of those ID. (#331)
 - popups: clicking outside (to close popup) and holding shouldn't drag window below.
 - popups: add variant using global identifier similar to Begin/End (#402)
 - popups: border options. richer api like BeginChild() perhaps? (#197)
 - popups/modals: although it is sometimes convenient that popups/modals lifetime is owned by imgui, we could also a bool-owned-by-user api as long as Begin() return value testing is enforced.

 - tooltip: drag and drop with tooltip near monitor edges lose/changes its last direction instead of locking one. The drag and drop tooltip should always follow without changing direction.
 - tooltip: allow to set the width of a tooltip to allow TextWrapped() etc. while keeping the height automatic.
 - tooltip: drag tooltip hovering over source widget with IsItemHovered/SetTooltip flickers (WIP branch)

 - status-bar: add a per-window status bar helper similar to what menu-bar does. generalize concept of layer0 rect in window (can make _MenuBar window flag obsolete too).
 - shortcuts: local-style shortcut api, e.g. parse "&Save"
 - shortcuts,menus: global-style shortcut api e.g. "Save (CTRL+S)" -> explicit flag for recursing into closed menu
 - shortcuts: programmatically access shortcuts "Focus("&Save"))
 - menus: menu-bar: main menu-bar could affect clamping of windows position (~ akin to modifying DisplayMin)
 - menus: hovering from menu to menu on a menu-bar has 1 frame without any menu, which is a little annoying. ideally either 0 either longer.
 - menus: would be nice if the Selectable() supported horizontal alignment (must be given the equivalent of WorkRect.Max.x matching the position of the shortcut column)

 - tree node: add treenode/treepush int variants? not there because (void*) cast from int warns on some platforms/settings?
 - tree node: try to apply scrolling at time of TreePop() if node was just opened and end of node is past scrolling limits?
 - tree node / selectable render mismatch which is visible if you use them both next to each other (e.g. cf. property viewer)
 - tree node: tweak color scheme to distinguish headers from selected tree node (#581)
 - tree node: leaf/non-leaf highlight mismatch.
 - tree node: flag to disable formatting and/or detect "%s"
 - tree node/opt: could avoid formatting when clipped (flag assuming we don't care about width/height, assume single line height? format only %s/%c to be able to count height?)

 - settings: write more decent code to allow saving/loading new fields: columns, selected tree nodes?
 - settings: api for per-tool simple persistent data (bool,int,float,columns sizes,etc.) in .ini file (#437)
 - settings/persistence: helpers to make TreeNodeBehavior persist (even during dev!) - may need to store some semantic and/or data type in ImGuiStoragePair

 - style: better default styles. (#707)
 - style: PushStyleVar: allow direct access to individual float X/Y elements.
 - style: add a highlighted text color (for headers, etc.)
 - style: border types: out-screen, in-screen, etc. (#447)
 - style: add window shadow (fading away from the window. Paint-style calculation of vertices alpha after drawlist would be easier)
 - style: a concept of "compact style" that the end-user can easily rely on (e.g. PushStyleCompact()?) that maps to other settings? avoid implementing duplicate helpers such as SmallCheckbox(), etc.
 - style: try to make PushStyleVar() more robust to incorrect parameters (to be more friendly to edit & continues situation).
 - style: global scale setting.
 - style: FramePadding could be different for up vs down (#584)
 - style: WindowPadding needs to be EVEN as the 0.5 multiplier used on this value probably have a subtle effect on clip rectangle
 - style: have a more global HSV setter (e.g. alter hue on all elements). consider replacing active/hovered by offset in HSV space? (#438, #707, #1223)
 - style: gradients fill (#1223) ~ 2 bg colors for each fill? tricky with rounded shapes and using textures for corners.
 - style editor: color child window height expressed in multiple of line height.

 - log: improve logging of ArrowButton, ListBox, TabItem
 - log: carry on indent / tree depth when opening a child window
 - log: enabling log ends up pushing and growing vertices buffers because we don't distinguish layout vs render clipping
 - log: have more control over the log scope (e.g. stop logging when leaving current tree node scope)
 - log: be able to log anything (e.g. right-click on a window/tree-node, shows context menu? log into tty/file/clipboard)
 - log: let user copy any window content to clipboard easily (CTRL+C on windows? while moving it? context menu?). code is commented because it fails with multiple Begin/End pairs.
 - log: obsolete LogButtons().... (was: LogButtons() options for specifying depth and/or hiding depth slider)

 - filters: set a current filter that certains items (e.g. tree node) can automatically query to hide themselves
 - filters: handle wild-cards (with implicit leading/trailing *), reg-exprs
 - filters: fuzzy matches (may use code at blog.forrestthewoods.com/4cffeed33fdb)

 - drag and drop: focus drag target window on hold (even without open)
 - drag and drop: releasing a drop shows the "..." tooltip for one frame - since e13e598 (#1725)
 - drag and drop: drag source on a group object (would need e.g. an invisible button covering group in EndGroup) https://twitter.com/paniq/status/1121446364909535233
 - drag and drop: have some way to know when a drag begin from BeginDragDropSource() pov. (see 2018/01/11 post in #143)
 - drag and drop: allow preview tooltip to be submitted from a different place than the drag source. (#1725)
 - drag and drop: make it easier and provide a demo to have tooltip both are source and target site, with a more detailed one on target site (tooltip ordering problem)
 - drag and drop: demo with reordering nodes (in a list, or a tree node). (#143)
 - drag and drop: test integrating with os drag and drop (make it easy to do a naive WM_DROPFILE integration)
 - drag and drop: allow for multiple payload types. (#143)
 - drag and drop: make payload optional? payload promise? (see 2018/01/11 post in #143)
 - drag and drop: (#143) "both an in-process pointer and a promise to generate a serialized version, for whether the drag ends inside or outside the same process"
 - drag and drop: feedback when hovering a region blocked by modal (mouse cursor "NO"?)

 - markup: simple markup language for color change? (#902, #3130)

 - text: selectable text (for copy) as a generic feature (ItemFlags?)
 - text: proper alignment options in imgui_internal.h
 - text: provided a framed text helper, e.g. https://pastebin.com/1Laxy8bT
 - text: refactor TextUnformatted (or underlying function) to more explicitly request if we need width measurement or not
 - text/layout/tabs: \t pulling position from base pos + step, or offset array (e.g. could be used in text edit, menus for simple icon+text alignment, etc.)
 - text link/url button: underlined. should api expose an ID or use text contents as ID? which colors enum to use?
 - text/wrapped: should be a more first-class citizen, e.g. wrapped text within a Selectable with known width.
 - text/wrapped: custom separator for text wrapping. (#3002)
 - text/wrapped: figure out better way to use TextWrapped() in an always auto-resize context (tooltip, etc.) (#249)

 - font: arbitrary line spacing. (#2945)
 - font: MergeMode: flags to select overwriting or not (this is now very easy with refactored ImFontAtlasBuildWithStbTruetype)
 - font: free the Alpha buffer if user only requested RGBA.
!- font: better CalcTextSizeA() API, at least for simple use cases. current one is horrible (perhaps have simple vs extended versions).
 - font: for the purpose of RenderTextEllipsis(), it might be useful that CalcTextSizeA() can ignore the trailing padding?
 - font: a CalcTextHeight() helper could run faster than CalcTextSize().y
 - font: enforce monospace through ImFontConfig (for icons?) + create dual ImFont output from same input, reusing rasterized data but with different glyphs/AdvanceX
 - font: finish CustomRectRegister() to allow mapping Unicode codepoint to custom texture data
 - font: remove ID from CustomRect registration, it seems unnecessary!
 - font: make it easier to submit own bitmap font (same texture, another texture?). (#2127, #2575)
 - font: PushFontSize API (#1018)
 - font: MemoryTTF taking ownership confusing/not obvious, maybe default should be opposite?
 - font: storing MinAdvanceX per font would allow us to skip calculating line width (under a threshold of character count) in loops looking for block width
 - font/demo: add tools to show glyphs used by a text blob, display U16 value, list missing glyphs.
 - font/demo: demonstrate use of ImFontGlyphRangesBuilder.
 - font/atlas: add a missing Glyphs.reserve()
 - font/atlas: incremental updates
 - font/atlas: dynamic font atlas to avoid baking huge ranges into bitmap and make scaling easier.
 - font/draw: vertical and/or rotated text renderer (#705) - vertical is easier clipping wise
 - font/draw: need to be able to specify wrap start position.
 - font/draw: better reserve policy for large horizontal block of text (shouldn't reserve for all clipped lines). also see #3349.
 - font/draw: fix for drawing 16k+ visible characters in same call.
 - font/draw: underline, squiggle line rendering helpers.
 - font: optimization: for monospace font (like the default one) we can trim IndexXAdvance as long as trailing value is == FallbackXAdvance (need to make sure TAB is still correct), would save on cache line.
 - font: add support for kerning, probably optional. A) perhaps default to (32..128)^2 matrix ~ 9K entries = 36KB, then hash for non-ascii?. B) or sparse lookup into per-char list?
 - font: add a simpler CalcTextSizeA() api? current one ok but not welcome if user needs to call it directly (without going through ImGui::CalcTextSize)
 - font: fix AddRemapChar() to work before atlas  has been built.
 - font: (api breaking) remove "TTF" from symbol names. also because it now supports OTF.
 - font/opt: Considering storing standalone AdvanceX table as 16-bit fixed point integer?
 - font/opt: Glyph currently 40 bytes (2+9*4). Consider storing UV as 16-bits integer? (->32 bytes). X0/Y0/X1/Y1 as 16 fixed-point integers? Or X0/Y0 as float and X1/Y1 as fixed8_8?

 - nav: visual feedback on button press.
 - nav: some features such as PageUp/Down/Home/End should probably work without ImGuiConfigFlags_NavEnableKeyboard? (where do we draw the line? how about CTRL+Tab)
 ! nav: never clear NavId on some setup (e.g. gamepad centric)
 - nav: there's currently no way to completely clear focus with the keyboard. depending on patterns used by the application to dispatch inputs, it may be desirable.
 - nav: Home/End behavior when navigable item is not fully visible at the edge of scrolling? should be backtrack to keep item into view?
 - nav: NavScrollToBringItemIntoView() with item bigger than view should focus top-right? Repro: using Nav in "About Window"
 - nav: wrap around logic to allow e.g. grid based layout (pressing NavRight on the right-most element would go to the next row, etc.). see internal's NavMoveRequestTryWrapping().
 - nav: patterns to make it possible for arrows key to update selection (see JustMovedTo in range_select branch)
 - nav: restore/find nearest NavId when current one disappear (e.g. pressed a button that disappear, or perhaps auto restoring when current button change name)
 - nav: SetItemDefaultFocus() level of priority, so widget like Selectable when inside a popup could claim a low-priority default focus on the first selected iem
 - nav: NavFlattened: init requests don't work properly on flattened siblings.
 - nav: NavFlattened: pageup/pagedown/home/<USER>'t work properly on flattened siblings.
 - nav: NavFlattened: ESC on a flattened child should select something.
 - nav: NavFlattened: broken: in typical usage scenario, the items of a fully clipped child are currently not considered to enter into a NavFlattened child.
 - nav: NavFlattened: cannot access menu-bar of a flattened child window with Alt/menu key (not a very common use case..).
 - nav: simulate right-click or context activation? (SHIFT+F10, keyboard Menu key?)
 - nav/popup: esc/enter default behavior for popups, e.g. be able to mark an "ok" or "cancel" button that would get triggered by those keys, default validation button, etc.
 - nav/treenode: left within a tree node block as a fallback (ImGuiTreeNodeFlags_NavLeftJumpsBackHere by default?)
 - nav/menus: pressing left-right on a vertically clipped menu bar tends to jump to the collapse/close buttons.
 - nav/menus: allow pressing Menu to leave a sub-menu.
 - nav/menus: a way to access the main menu bar with Alt? (currently needs CTRL+TAB) or last focused window menu bar?
 - nav/menus: when using the main menu bar, even though we restore focus after, the underlying window loses its title bar highlight during menu manipulation. could we prevent it?
 - nav/menus: main menu bar currently cannot restore a nullptr focus. Could save NavWindow at the time of being focused, similarly to what popup do?
 - nav/menus: Alt,Up could open the first menu (e.g. "File") currently it tends to nav into the window/collapse menu. Do do that we would need custom transition?
 - nav/windowing: when CTRL+Tab/windowing is active, the HoveredWindow detection doesn't take account of the window display re-ordering.
 - nav/windowing: Resizing window will currently fail with certain types of resizing constraints/callback applied
 - focus: preserve ActiveId/focus stack state, e.g. when opening a menu and close it, previously selected InputText() focus gets restored (#622)

 - inputs: support track pad style scrolling & slider edit.
 - inputs/io: backspace and arrows in the context of a text input could use system repeat rate.
 - inputs/io: clarify/standardize/expose repeat rate and repeat delays (#1808)
 - inputs/scrolling: support for smooth scrolling (#2462, #2569)

 - misc: idle: expose "woken up" boolean (set by inputs) and/or animation time (for cursor blink) for backend to be able stop refreshing easily.
 - misc: idle: if cursor blink if the _only_ visible animation, core imgui could rewrite vertex alpha to avoid CPU pass on ImGui:: calls.
 - misc: idle: if cursor blink if the _only_ visible animation, could even expose a dirty rectangle that optionally can be leverage by some app to render in a smaller viewport, getting rid of much pixel shading cost.
 - misc: no way to run a root-most GetID() with ImGui:: api since there's always a Debug window in the stack. (mentioned in #2960)
 - misc: make the ImGuiCond values linear (non-power-of-two). internal storage for ImGuiWindow can use integers to combine into flags (Why?)
 - misc: PushItemFlag(): add a flag to disable keyboard capture when used with mouse? (#1682)
 - misc: use more size_t in public api?
 - misc: possible compile-time support for string view/range instead of char* would e.g. facilitate usage with Rust (#683, #3038, WIP string_view branch)
 - misc: possible compile-time support for wchar_t instead of char*?

 - demo: demonstrate using PushStyleVar() in more details.
 - demo: add vertical separator demo
 - demo: add virtual scrolling example?
 - demo: demonstrate Plot offset
 - demo: window size constraint: square demo is broken when resizing from edges (#1975), would need to rework the callback system to solve this

 - examples: window minimize, maximize (#583)
 - examples: provide a zero frame-rate/idle example.
 - examples: dx11/dx12: try to use new swapchain blit models (#2970)
 - backends: report it better when not able to create texture?
 - backends: glfw: could go idle when minimized? if (glfwGetWindowAttrib(window, GLFW_ICONIFIED)) { glfwWaitEvents(); continue; } // issue: DeltaTime will be super high on resume, perhaps provide a way to let impl know (#440)
 - backends: opengl: rename imgui_impl_opengl2 to impl_opengl_legacy and imgui_impl_opengl3 to imgui_impl_opengl? (#1900)
 - backends: opengl: could use a single vertex buffer and glBufferSubData for uploads?
 - backends: opengl: explicitly disable GL_STENCIL_TEST in bindings.
 - backends: vulkan: viewport: support for synchronized swapping of multiple swap chains.
 - backends: bgfx: https://gist.github.com/RichardGale/6e2b74bc42b3005e08397236e4be0fd0
 - backends: emscriptem: with refactored examples, we could provide a direct imgui_impl_emscripten platform layer (see eg. https://github.com/floooh/sokol-samples/blob/master/html5/imgui-emsc.cc#L42)

 - bindings: ways to use clang ast dump to generate bindings or helpers for bindings? (e.g. clang++ -Xclang -ast-dump=json imgui.h) (WIP project "dear-bindings" still private)

 - optimization: replace vsnprintf with stb_printf? using IMGUI_USE_STB_SPRINTF. (#1038 + needed for string_view)
 - optimization: add clipping for multi-component widgets (SliderFloatX, ColorEditX, etc.). one problem is that nav branch can't easily clip parent group when there is a move request.
 - optimization: add a flag to disable most of rendering, for the case where the user expect to skip it (#335)
 - optimization: fully covered window (covered by another with non-translucent bg + WindowRounding worth of padding) may want to clip rendering.
 - optimization: use another hash function than crc32, e.g. FNV1a
 - optimization: turn some the various stack vectors into statically-sized arrays
